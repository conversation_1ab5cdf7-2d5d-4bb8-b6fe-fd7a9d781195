# -*- coding:utf-8 -*-
# Author:      zhoushuangfeng
# Date:        2019-12-27
# File:        encrypt_project.py
# Description:
# 1. 安装依赖库
#     pip3 --default-timeout=1000 install -U cython
#     sudo apt-get  build-dep  gcc
# 2. 工程的所有py文件的当前目录以及所有上级目录下都要有__init__.py文件，若没有请新建
# 3. 在工程根目录下或非工程目录外新建build_so目录并将encrypt_project.py复制到build_so目录下
# 4. 设置工程根目录project_dir地址
# 5. 终端中运行 python3.6 encrypt_project.py build_ext --inplace
# 6. build_so目录下会生成工程所有的so文件和资源文件
# 注意：flask的app需要加入exclude_dirs_or_files中，否则服务运行不起来

from setuptools import setup
from Cython.Build import cythonize
import os
import shutil

# 工程根目录
src_dir = os.path.realpath("../src")
project_dir = os.path.dirname(src_dir)
project_name = os.path.basename(project_dir)

# 过滤目录或文件-包含的文件目录下文件不会生成so
exclude_dirs_or_files = []


def copy_file(current_path, build_dir_name, root, current_file):
    _, child_dir = root.split(build_dir_name)
    if len(child_dir) > 0:
        target_dir = current_path + "/" + build_dir_name + child_dir
    else:
        target_dir = current_path + "/" + build_dir_name
    os.makedirs(target_dir, exist_ok=True)
    shutil.copy(current_file, target_dir)


def distill_dirs_or_files(root):
    for exclude in exclude_dirs_or_files:
        if root.find(exclude) >= 0:
            return True
    return False


def main():
    build_dir_name = os.path.basename(src_dir)
    current_path = os.getcwd()
    build_path = os.path.join(current_path, "build")
    try:

        for root, dirs, files in os.walk(src_dir):
            for file in files:
                current_file = os.path.join(root, file)
                print(current_file)
                # 过滤 py 编译文件
                if file.endswith(".pyc"):
                    continue
                if file.endswith(".c"):
                    continue
                # 过滤当前文件
                if current_file == __file__:
                    continue
                # 过滤build文件夹
                if root.find(build_path) >= 0:
                    continue
                # 过滤当前文件夹
                if root.find(current_path) >= 0:
                    continue
                # 过滤指定目录
                if distill_dirs_or_files(root):
                    continue
                # 过滤指定文件
                if current_file in exclude_dirs_or_files:
                    continue
                # 复制非 py 文件
                if not file.endswith(".py"):
                    copy_file(current_path, build_dir_name, root, current_file)
                    continue

                # 编译 py 文件
                print(f"Building: {current_file}")
                build_subdir_path = os.path.join(
                    current_path,
                    build_dir_name,
                    os.path.relpath(os.path.dirname(current_file), src_dir),
                )
                os.makedirs(build_subdir_path, exist_ok=True)
                setup(ext_modules=cythonize([current_file]))

                # 删除 .c 文件以保证每次都进行 so 文件生成
                name, _ = file.split(".")
                c_file = os.path.join(root, name + ".c")
                if os.path.exists(c_file):
                    os.remove(c_file)

        # 删除 build 目录
        if os.path.exists(build_path):
            shutil.rmtree(build_path)
            print(file)

        os.makedirs("app", exist_ok=True)
        shutil.move(build_dir_name, "app")
        shutil.copy(f"{project_dir}/run.py", "app")
        shutil.copy(f"{project_dir}/requirements.txt", "app")

        print("done! Generating files completed.")
        print("Build dir: " + current_path)

    except Exception as ex:
        if os.path.exists(build_path):
            shutil.rmtree(build_path)


if __name__ == "__main__":
    main()
