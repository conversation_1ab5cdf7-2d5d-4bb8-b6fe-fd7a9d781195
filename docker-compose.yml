name: smartstrm
services:
  smartstrm:
    # build: .
    # image: cp0204/smartstrm:latest
    image: registry.cn-shenzhen.aliyuncs.com/cp0204/smartstrm:main
    container_name: smartstrm
    restart: unless-stopped
    network_mode: host
    volumes:
      - /DATA/AppData/smartstrm/config:/app/config
      - /DATA/AppData/smartstrm/logs:/app/logs
      - /DATA/AppData/smartstrm/strm:/strm
    environment:
      - PORT=8024
      - DEBUG=false
      - ADMIN_USERNAME=admin
      - ADMIN_PASSWORD=admin123
      - LICENSE=