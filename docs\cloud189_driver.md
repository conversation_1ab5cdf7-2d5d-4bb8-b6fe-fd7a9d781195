# 天翼云盘驱动 (Cloud189Driver)

## 概述

天翼云盘驱动是一个完全独立的驱动实现，直接集成天翼云盘API，无需依赖外部SDK。该驱动提供对中国电信天翼云盘的完整支持，实现了 SmartStrm 所需的所有基础功能，包括文件列表、下载、删除等操作，并直接使用配置管理器来自动维护登录状态。

## 特性

- ✅ **自动登录状态维护**: 使用配置管理器自动保存和恢复登录状态
- ✅ **Token 自动刷新**: 当 access token 失效时自动使用 refresh token 刷新
- ✅ **失效重新登录**: 当 refresh token 也失效时自动使用用户名密码重新登录
- ✅ **文件操作**: 支持文件列表、删除等基本操作
- ✅ **下载支持**: 支持直链下载和代理下载两种模式
- ✅ **STRM 支持**: 支持生成 STRM 文件用于媒体播放
- ✅ **缓存优化**: 文件夹 ID 和下载链接缓存，提高访问速度

## 配置参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | string | 是 | 天翼云盘登录用户名（手机号） |
| password | password | 是 | 天翼云盘登录密码 |
| strm_mode | select | 否 | STRM 模式：direct（直链）或 proxy（代理），默认 direct |
| access_token | hidden | 否 | 自动维护的访问令牌 |
| refresh_token | hidden | 否 | 自动维护的刷新令牌 |
| session_key | hidden | 否 | 自动维护的会话密钥 |
| user_info | hidden | 否 | 自动维护的用户信息 |

## 使用方法

### 1. 添加存储

在 SmartStrm 管理界面中：

1. 点击"添加存储"
2. 选择"天翼云盘"驱动类型
3. 填写用户名（手机号）和密码
4. 选择 STRM 模式（推荐使用直链模式）
5. 保存配置

### 2. 登录状态管理

驱动会自动处理登录状态：

- **首次使用**: 使用用户名密码登录，获取 access token 和 refresh token
- **后续使用**: 优先从配置中恢复已保存的 token
- **Token 刷新**: access token 失效时自动使用 refresh token 刷新
- **重新登录**: refresh token 失效时自动使用用户名密码重新登录

### 3. STRM 模式选择

- **直链模式 (direct)**: STRM 文件直接指向天翼云盘的下载链接，播放器直接访问
- **代理模式 (proxy)**: STRM 文件指向 SmartStrm 代理链接，通过 SmartStrm 中转访问

## 支持的操作

### 文件操作
- `list_files(path)`: 列出指定路径下的文件和文件夹
- `delete_file(path)`: 删除指定文件或文件夹
- `rename_file(path, new_name)`: 重命名文件（暂不支持）

### 下载操作
- `get_download_url(path, file_info)`: 获取文件下载链接
- `get_strm_url(path, file_info)`: 获取 STRM 播放链接
- `get_file_data(path, file_info)`: 获取文件二进制数据

### 辅助操作
- `get_play_url(path, file_info)`: 获取视频播放链接
- `get_space_info()`: 获取存储空间信息
- `init_info()`: 初始化用户信息

## 技术实现

### 直接配置管理器集成

```python
def _save_tokens(self):
    """保存token到配置"""
    try:
        config = self.config_manager.get_storage(self.name)
        if config:
            config.update({
                "access_token": self.access_token,
                "refresh_token": self.refresh_token,
                "session_key": self.session_key,
            })
            return self.config_manager.update_storage(self.name, config)
        return False
    except Exception as e:
        logger.error(f"保存token失败: {e}")
        return False
```

### 自动登录流程

1. **初始化驱动**: 从配置中读取已保存的 access_token、refresh_token、session_key
2. **Token 验证**: 优先验证 session_key 是否有效
3. **Token 恢复**: 如果 session_key 失效，使用 access_token 重新获取
4. **自动刷新**: 如果 access_token 失效，使用 refresh_token 刷新
5. **重新登录**: 如果 refresh_token 也失效，使用用户名密码重新登录
6. **状态保存**: 所有新的 token 自动保存到配置管理器

## 注意事项

1. **网络要求**: 需要能够访问天翼云盘 API (cloud.189.cn)
2. **账号安全**: 密码会保存在配置文件中，请确保配置文件安全
3. **API 限制**: 天翼云盘可能有 API 调用频率限制
4. **重命名限制**: 当前版本暂不支持文件重命名功能
5. **加密简化**: 当前版本简化了RSA加密处理，可能需要进一步完善
6. **独立实现**: 完全独立实现，无需外部SDK依赖

## 故障排除

### 登录失败
- 检查用户名密码是否正确
- 确认网络能够访问天翼云盘
- 查看日志中的详细错误信息

### Token 失效
- 驱动会自动处理 token 失效问题
- 如果持续失败，可以删除配置中的 token_data 强制重新登录

### 文件访问失败
- 检查文件路径是否正确
- 确认文件是否存在
- 查看是否有权限访问该文件

## 开发信息

- **驱动类型**: cloud189
- **实现方式**: 独立实现，直接集成天翼云盘API
- **支持版本**: SmartStrm v1.0+
- **维护状态**: 活跃开发中
- **依赖**: 仅依赖标准库和requests
