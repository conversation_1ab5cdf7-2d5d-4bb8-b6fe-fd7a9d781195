import os
from ruamel.yaml import YAML
from ruamel.yaml.comments import CommentedMap, CommentedSeq
from ruamel.yaml.scalarstring import DoubleQuotedScalarString, SingleQuotedScalarString
from typing import Dict, List, Any, Optional
from core.log_manager import LogManager


logger = LogManager.get_logger(__name__)


class ConfigManager:
    def __init__(self, config_path: str):
        """初始化配置管理器

        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.yaml = YAML()
        self.yaml.preserve_quotes = True
        self.yaml.indent(mapping=2, sequence=4, offset=2)
        self.config = None
        self._load_config()

    def _c(self, data: Any) -> Any:
        """将 CommentedMap 和 CommentedSeq 转换为标准的 Python dict 和 list

        Args:
            data: 要转换的数据

        Returns:
            Any: 转换后的数据
        """
        if isinstance(data, CommentedMap):
            return {k: self._c(v) for k, v in data.items()}
        elif isinstance(data, CommentedSeq):
            return [self._c(item) for item in data]
        elif isinstance(data, DoubleQuotedScalarString):
            return str(data)
        elif isinstance(data, SingleQuotedScalarString):
            return str(data)
        return data

    def _load_config(self):
        """加载配置文件"""
        default_config = {
            "storages": [],
            "tasks": [],
            "settings": {
                "init": False,
                "strm": {
                    "media_ext": ["mp4", "mkv", "mov", "avi"],
                    "media_size": 20,
                    "copy_ext": ["nfo", "jpg", "png", "ass", "srt"],
                    "save_dir": "/strm",
                    "url_encode": True,
                    "smartstrm_base": "http://172.17.0.1:8024",
                },
                "rename": {
                    "movie": "{TITLE}.{YEAR}.{EXT}",
                    "tv": "{TITLE}.{SXX}E{E}.{EXT}",
                },
                "proxy": {
                    "enabled": False,
                    "port": 8097,
                    "target": "http://172.17.0.1:8096",
                },
                "webhook": {
                    "emby_delete_sync": {
                        "enabled": False,
                        "strm_in_emby": "/strm",
                    }
                },
            },
        }
        if not os.path.exists(self.config_path):
            self.config = default_config
            self._save_config()
            return
        try:
            with open(self.config_path, "r", encoding="utf-8") as f:
                self.config = self.yaml.load(f)
                # 合并默认配置，处理配置升级
                self.config = self._merge_dict(self.config, default_config)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            self.config = default_config

    def _merge_dict(self, user: Any, default: Dict[str, Any]) -> Any:
        """合并用户配置和默认配置

        Args:
            default: 默认配置
            user: 用户配置

        Returns:
            Dict[str, Any]: 合并后的配置
        """
        for key, value in default.items():
            if key not in user:
                user[key] = value
            elif isinstance(value, dict):
                user[key] = self._merge_dict(user[key], value)
        return user

    def _save_config(self):
        """保存配置文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            with open(self.config_path, "w", encoding="utf-8") as f:
                self.yaml.dump(self.config, f)
            return True
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            return False

    def get_storages(self) -> List[Dict[str, Any]]:
        """获取所有存储配置"""
        return self._c(self.config.get("storages", [])).copy()

    def get_storage(self, name: str) -> Optional[Dict[str, Any]]:
        """获取指定存储配置"""
        for storage in self.config.get("storages", []):
            if storage["name"] == name:
                return self._c(storage).copy()
        return None

    def update_storage(self, name: str, storage_config: Dict[str, Any]) -> bool:
        """更新存储配置

        Args:
            name: 存储名称
            storage_config: 存储配置

        Returns:
            bool: 是否更新成功
        """
        try:
            # 更新存储
            for i, storage in enumerate(self.config["storages"]):
                if storage["name"] == name:
                    self.config["storages"][i] = storage_config
                    return self._save_config()
            # 新增存储
            self.config["storages"].append(storage_config)
            return self._save_config()
        except Exception as e:
            logger.error(f"更新存储配置失败: {e}")
            return False

    def delete_storage(self, name: str) -> bool:
        """删除存储配置

        Args:
            name: 存储名称

        Returns:
            bool: 是否删除成功
        """
        try:
            for i, storage in enumerate(self.config["storages"]):
                if storage["name"] == name:
                    del self.config["storages"][i]
                    return self._save_config()
            return False
        except Exception as e:
            logger.error(f"删除存储配置失败: {e}")
            return False

    def get_tasks(self) -> List[Dict[str, Any]]:
        """获取所有任务配置"""
        return self._c(self.config.get("tasks", [])).copy()

    def get_task(self, name: str) -> Optional[Dict[str, Any]]:
        """获取指定任务配置"""
        for task in self.config.get("tasks", []):
            if task["name"] == name:
                return self._c(task).copy()
        return None

    def update_task(self, name: str, task_config: Dict[str, Any]) -> bool:
        """更新任务配置

        Args:
            name: 任务名称
            task_config: 任务配置

        Returns:
            bool: 是否更新成功
        """
        try:
            # 更新任务
            for i, task in enumerate(self.config["tasks"]):
                if task["name"] == name:
                    self.config["tasks"][i] = task_config
                    return self._save_config()
            # 新增任务
            self.config["tasks"].append(task_config)
            return self._save_config()
        except Exception as e:
            logger.error(f"更新任务配置失败: {e}")
            return False

    def delete_task(self, name: str) -> bool:
        """删除任务配置

        Args:
            name: 任务名称

        Returns:
            bool: 是否删除成功
        """
        try:
            for i, task in enumerate(self.config["tasks"]):
                if task["name"] == name:
                    del self.config["tasks"][i]
                    return self._save_config()
            return False
        except Exception as e:
            logger.error(f"删除任务配置失败: {e}")
            return False

    def get_settings(self) -> Dict[str, Any]:
        """获取系统设置

        Returns:
            Dict[str, Any]: 系统设置
        """
        return self._c(self.config.get("settings", {})).copy()

    def update_settings(self, settings: Dict[str, Any]) -> bool:
        """更新系统设置

        Args:
            settings: 系统设置

        Returns:
            bool: 是否更新成功
        """
        try:
            self.config["settings"].update(settings)
            return self._save_config()
        except Exception as e:
            logger.error(f"更新系统设置失败: {e}")
            return False
