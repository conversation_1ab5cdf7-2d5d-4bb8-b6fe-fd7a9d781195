import os
import logging
from logging import (
    get<PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>er,
)
from logging.handlers import TimedRotatingFileHandler, RotatingFileHandler
from dotenv import load_dotenv
import collections
import threading

# 加载环境变量
load_dotenv()
DEBUG = os.getenv("DEBUG", "").lower() == "true"


# 自定义日志处理器，将日志放入队列
class ClientLogHandler(logging.Handler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._client_log_maxsize = 100
        self._client_active = []
        self._client_lock = threading.Lock()

    def emit(self, record):
        """将格式化后的日志消息放入队列"""
        msg = self.format(record)
        with self._client_lock:
            # 创建一个副本进行迭代，防止在迭代过程中集合被修改
            # 尤其是在客户端断开连接时，可能从集合中移除其队列
            queues_to_broadcast = list(self._client_active)
        for client_q in queues_to_broadcast:
            try:
                client_q.append(msg)
            except Exception as e:
                pass

    def register(self):
        """注册一个新的客户端队列，并返回该队列"""
        # client_queue = queue.Queue(maxsize=self._client_queue_maxsize)
        client = collections.deque(maxlen=self._client_log_maxsize)
        with self._client_lock:
            self._client_active.append(client)
        return client

    def unregister(self, client):
        """注销一个客户端队列"""
        with self._client_lock:
            if client in self._client_active:
                self._client_active.remove(client)


class LogManager:
    """日志管理器，用于统一管理日志格式和配置"""

    # 日志级别映射
    LOG_LEVELS = {
        "debug": logging.DEBUG,
        "info": logging.INFO,
        "warning": logging.WARNING,
        "error": logging.ERROR,
        "critical": logging.CRITICAL,
    }

    def __init__(self, log_dir: str = "logs"):
        """初始化日志管理器

        Args:
            log_dir: 日志文件目录
        """
        self.log_dir = log_dir
        self.log_level = self._get_log_level("LOG_LEVEL", "info")
        self.task_logger_handlers = {}
        self._setup_logging()

    def _get_log_level(self, env_var: str, default: str = "info") -> int:
        """从环境变量获取日志级别

        Args:
            env_var: 环境变量名
            default: 默认日志级别

        Returns:
            int: 日志级别
        """
        if DEBUG:
            return logging.DEBUG
        level = os.getenv(env_var, default).lower()
        return self.LOG_LEVELS[level]

    def _setup_logging(self):
        """配置日志系统"""
        # 确保日志目录存在
        os.makedirs(self.log_dir, exist_ok=True)

        # 获取根日志记录器
        root_logger = getLogger()
        root_logger.setLevel(logging.DEBUG)

        # 清除现有的处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # 创建日志格式
        if self.log_level == logging.DEBUG:
            log_format = Formatter(
                "%(asctime)s [%(levelname)s] [%(name)s] %(message)s",
                datefmt="%Y-%m-%d %H:%M:%S",
            )
        else:
            log_format = Formatter(
                "%(asctime)s [%(levelname)s] %(message)s",
                datefmt="%m-%d %H:%M:%S",
            )

        # 添加控制台处理器
        console_handler = StreamHandler()
        console_handler.setFormatter(log_format)
        console_handler.setLevel(self._get_log_level("LOG_LEVEL", "info"))
        root_logger.addHandler(console_handler)

        # 添加文件处理器（按日期滚动）
        log_file = os.path.join(self.log_dir, "smartstrm.log")
        file_handler = TimedRotatingFileHandler(
            filename=log_file,
            when="midnight",  # 每天午夜切换
            interval=1,  # 每1天
            backupCount=30,  # 保留30天的日志
            encoding="utf-8",
            atTime=None,  # 使用默认的午夜时间
        )
        file_handler.setFormatter(log_format)
        file_handler.setLevel(self._get_log_level("FILE_LOG_LEVEL", "info"))
        # 设置日志文件的后缀格式为日期
        file_handler.suffix = "%Y%m%d.log"
        root_logger.addHandler(file_handler)

        # 设置第三方库的日志级别
        module_level = self._get_log_level("MODULE_LOG_LEVEL", "warning")
        logging.getLogger("urllib3").setLevel(module_level)
        logging.getLogger("requests").setLevel(module_level)
        logging.getLogger("apscheduler").setLevel(module_level)
        logging.getLogger("werkzeug").setLevel(module_level)
        logging.getLogger("aiohttp").setLevel(module_level)

        # 记录日志配置
        logger = self.get_logger(__name__)
        logger.info(f"日志系统初始化完成")
        logger.debug(f"根日志级别: {logging.getLevelName(root_logger.level)}")
        logger.debug(f"控制台日志级别: {logging.getLevelName(console_handler.level)}")
        logger.debug(f"文件日志级别: {logging.getLevelName(file_handler.level)}")
        logger.debug(f"第三方库日志级别: {logging.getLevelName(module_level)}")

    @staticmethod
    def get_logger(name: str) -> logging.Logger:
        """获取指定名称的日志记录器

        Args:
            name: 日志记录器名称

        Returns:
            logging.Logger: 日志记录器实例
        """
        return logging.getLogger(name)

    @staticmethod
    def get_task_logger(task_name: str) -> logging.Logger:
        """获取任务专用的日志记录器

        Args:
            task_name: 任务名称

        Returns:
            logging.Logger: 任务日志记录器实例
        """
        task_logger = logging.getLogger(f"task.{task_name}")

        if not hasattr(task_logger, "initialized"):
            # 创建任务日志文件处理器
            log_file_path = os.path.join("logs", "tasks", f"{task_name}.log")
            # 确保任务日志目录存在
            os.makedirs(os.path.dirname(log_file_path), exist_ok=True)
            task_file_handler = RotatingFileHandler(
                log_file_path,
                maxBytes=1024 * 1024 * 1,  # 1MB
                backupCount=0,  # 不保留旧文件，直接覆盖
                encoding="utf-8",
            )
            # 使用任务专用的日志格式
            task_log_format = Formatter(
                "%(asctime)s %(message)s",
                datefmt="%m-%d %H:%M:%S",
            )
            task_file_handler.setFormatter(task_log_format)
            task_logger_level = logging.DEBUG if DEBUG else logging.INFO
            task_file_handler.setLevel(task_logger_level)
            # 添加处理器到任务日志记录器
            task_logger.addHandler(task_file_handler)

            # 添加队列处理器，用于推送日志到前端
            task_logger.client_handler = ClientLogHandler()
            task_logger.client_handler.setLevel(task_logger_level)
            task_logger.client_handler.setFormatter(task_log_format)
            task_logger.addHandler(task_logger.client_handler)

            # 防止日志向上传播到根日志记录器
            # task_logger.propagate = False
            # 记录已创建的任务日志处理器
            task_logger.initialized = True

        return task_logger
