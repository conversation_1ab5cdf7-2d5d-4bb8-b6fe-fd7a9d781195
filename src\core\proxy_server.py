import os
import sys
import json
import asyncio
import aiohttp
import urllib.parse
from aiohttp import web
from utils import utils
from utils.kv_cache import KVCache
from typing import Any, Dict, Optional
from multiprocessing import Process, Queue
from core.log_manager import LogManager
from core.config_manager import ConfigManager

logger = LogManager.get_logger(__name__)

# ENV
SMARTSTRM_PORT = os.getenv("PORT", 8024)


class ProxyServer:
    def __init__(self, config_path: str, status_queue: Queue = None):
        self.config_path = config_path
        self.config = ConfigManager(config_path).get_settings().get("proxy", {})
        self.app = web.Application()
        self.status_queue = status_queue
        self.process = None
        self.kv_cache = KVCache()
        self._setup_routes()

    def _setup_routes(self):
        def add_route(method, path, handler):
            self.app.router.add_route(method, path, handler)

        # 处理 Emby PlaybackInfo 请求
        add_route(
            "*", r"/emby/Items/{id:\d+}/PlaybackInfo", self._handle_emby_playbackinfo
        )
        # 处理 Emby CORS
        add_route("*", "/web/index.html", self._handle_emby_index)
        add_route(
            "*",
            "/web/modules/htmlvideoplayer/basehtmlplayer.js{query:.*}",
            self._handle_emby_basehtmlplayer,
        )
        # 处理 Jellyfin PlaybackInfo 请求
        add_route(
            "*", r"/Items/{id:\w+}/PlaybackInfo", self._handle_jellyfin_playbackinfo
        )
        # 拦截 original 请求，兼容 AfuseKt
        add_route(
            "*",
            r"/{emby:(emby/)?}videos/{id:\w+}/{file:(original|stream)[^\?]*}",
            self._handle_original,
        )
        # 智能 302
        add_route("*", "/{emby:(emby/)?}smartstrm", self._handle_smartstrm)
        # 默认代理转发请求
        add_route("*", "/{path:.*}", self._handle_request)

    async def _handle_test(self, request: web.Request) -> web.Response:
        """处理测试请求"""
        return web.Response(status=200, text="test")

    async def _handle_smartstrm(self, request: web.Request) -> web.Response:
        """处理302请求"""
        item_id = request.query.get("item_id")
        media_id = request.query.get("media_id")

        # 读取缓存
        target_url = self.kv_cache.get(media_id)
        # or urllib.parse.unquote(request.query.get("url"))
        if not target_url:
            return web.Response(status=404, text="MediaItem Error")

        async def try_proxy_url(parsed_url):
            if utils.is_lan_ip(parsed_url.netloc):
                # 内网 IP，本机代理
                full_path = utils.url_full_path(target_url)
                request_c = request.clone(rel_url=full_path)
                return await self._handle_request(
                    request_c, target=f"http://{parsed_url.netloc}"
                )
            return web.HTTPFound(location=target_url)

        parsed_url = urllib.parse.urlparse(target_url)
        # 智能 302 转发
        if parsed_url.path.startswith("/smartstrm/"):
            # SmartStrm
            logger.info(f"MediaItem: {item_id} 命中 SmartStrm 302")
            return await try_proxy_url(parsed_url)
        elif parsed_url.path.startswith("/d/"):
            # OpenList
            logger.info(f"MediaItem: {item_id} 命中 OpenList 302")
            return await try_proxy_url(parsed_url)
        else:
            # 其他 http 链接
            logger.info(f"MediaItem: {item_id} 命中常规 Strm 302")

        return web.HTTPFound(location=target_url)

    async def _get_response(
        self, request: web.Request, target: str = None
    ) -> web.Response:
        """获取原始响应体"""
        try:
            # 获取目标URL
            url = self._get_target_url(request, target)
            if not url:
                return web.Response(status=404, text="No target URL configured")
            # 准备请求头
            headers = self._prepare_headers(request, self.config.get("headers", {}))
            # 准备请求体
            body = await self._prepare_body(request)
            # 发送请求到目标服务器
            async with aiohttp.ClientSession() as session:
                async with session.request(
                    method=request.method,
                    url=url,
                    headers=headers,
                    data=body,
                    allow_redirects=True,
                ) as response:
                    # 准备响应头
                    resp_headers = self._prepare_response_headers(response)
                    # 获取原始响应体
                    original_body = await response.content.read()
                    # 创建响应
                    response = web.Response(
                        body=original_body, status=response.status, headers=resp_headers
                    )
                    return response
        except Exception as e:
            logger.error(f"Proxy error: {str(e)}")
            return web.Response(status=500, text=str(e))

    async def _handle_emby_index(self, request: web.Request) -> web.Response:
        """处理 Emby /web/index.html"""
        response = await self._get_response(request)
        response.body = response.body.replace(
            b'<link rel="manifest" href="manifest.json">',
            b'<link rel="manifest" href="manifest.json">\n    <meta name="referrer" content="no-referrer">',
        )
        return response

    async def _handle_emby_basehtmlplayer(self, request: web.Request) -> web.Response:
        """处理 Emby /web/modules/htmlvideoplayer/basehtmlplayer.js"""
        response = await self._get_response(request)
        response.body = response.body.replace(
            b'return mediaSource.IsRemote&&"DirectPlay"===playMethod?null:"anonymous"',
            b"return null",
        )
        return response

    async def _handle_emby_playbackinfo(self, request: web.Request) -> web.Response:
        """处理 Emby PlaybackInfo 请求"""
        item_id = request.match_info.get("id")
        response = await self._get_response(request)
        try:
            if response.status != 200:
                return response
            data = json.loads(response.body)
            # 判断 http的播放链接 或 /开头的本地文件
            media_path = data["MediaSources"][0]["Path"]
            if media_path.startswith("http"):
                media_id = data["MediaSources"][0]["Id"]
                self.kv_cache.set(media_id, media_path, 60 * 60 * 24)
                data["MediaSources"][0].update(
                    {
                        # 强制禁止转码
                        "SupportsDirectPlay": True,
                        "SupportsDirectStream": True,
                        "SupportsTranscoding": False,
                        "TranscodingUrl": "",
                        "TranscodingSubProtocol": "",
                        "TranscodingContainer": "",
                        # 魔改直链
                        "DirectStreamUrl": f"/smartstrm?item_id={item_id}&media_id={media_id}",
                    }
                )
            response.body = json.dumps(data, ensure_ascii=False)
            return response
        except Exception as e:
            logger.error(f"error: {str(e)}")
        return response

    async def _handle_jellyfin_playbackinfo(self, request: web.Request) -> web.Response:
        """处理 Jellyfin PlaybackInfo 请求"""
        item_id = request.match_info.get("id")
        response = await self._get_response(request)
        try:
            if response.status != 200:
                return response
            data = json.loads(response.body)
            # 判断 http的播放链接 或 /开头的本地文件
            media_path = data["MediaSources"][0]["Path"]
            if media_path.startswith("http"):
                media_id = data["MediaSources"][0]["Id"]
                self.kv_cache.set(media_id, media_path, 60 * 60 * 24)
                data["MediaSources"][0].update(
                    {
                        # 强制禁止转码
                        "SupportsDirectPlay": True,
                        "SupportsDirectStream": True,
                        "SupportsTranscoding": False,
                        "TranscodingURL": "",
                        "TranscodingSubProtocol": "",
                        "TranscodingContainer": "",
                        # 魔改直链
                        "Path": f"/smartstrm?item_id={item_id}&media_id={media_id}",
                    }
                )
            response.body = json.dumps(data, ensure_ascii=False)
            return response
        except Exception as e:
            logger.error(f"error: {str(e)}")
        return response

    async def _handle_original(self, request: web.Request) -> web.Response:
        """处理 original 请求"""
        item_id = request.match_info.get("id")
        is_emby = request.match_info.get("emby", "")
        stream_file = request.match_info.get("file", "")
        try:
            # 请求 PlaybackInfo 播放信息
            request_c = request.clone(
                rel_url=f"/{is_emby}Items/{item_id}/PlaybackInfo?{request.query_string}"
            )
            response = await self._get_response(request_c)
            if response.status == 200:
                data = json.loads(response.body)
                # 判断路径是否 http 开头的 STRM
                media_path = data["MediaSources"][0]["Path"]
                if media_path.startswith("http"):
                    media_id = data["MediaSources"][0]["Id"]
                    self.kv_cache.set(media_id, media_path, 60 * 60 * 24)
                    logger.debug(f"MediaItem: {item_id} 命中 {stream_file} 302")
                    request_c = request_c.clone(
                        rel_url=f"/smartstrm?item_id={item_id}&media_id={media_id}"
                    )
                    return await self._handle_smartstrm(request_c)
        except Exception as e:
            logger.error(f"original 302 error: {str(e)}")
        return await self._handle_request(request)

    async def _handle_request(
        self, request: web.Request, target: str = None
    ) -> web.Response:
        """默认代理转发请求"""
        try:
            # 检测是否为WebSocket升级请求
            if self._is_websocket_request(request):
                return await self._handle_websocket(request, target)

            # 获取目标URL
            url = self._get_target_url(request, target)
            if not url:
                return web.Response(status=404, text="No target URL configured")
            # 准备请求头
            headers = self._prepare_headers(request, self.config.get("headers", {}))
            # 准备请求体
            body = await self._prepare_body(request)
            # 发送请求到目标服务器
            async with aiohttp.ClientSession() as session:
                async with session.request(
                    method=request.method,
                    url=url,
                    headers=headers,
                    data=body,
                    allow_redirects=False,
                ) as response:
                    # 准备响应头
                    resp_headers = self._prepare_response_headers(response)

                    # 检查响应状态码，返回响应头，无须传输数据
                    if 300 <= response.status < 400:
                        return web.Response(
                            status=response.status, headers=resp_headers
                        )

                    # 创建流式响应
                    stream_response = web.StreamResponse(
                        status=response.status, headers=resp_headers
                    )
                    await stream_response.prepare(request)
                    async for chunk in response.content.iter_chunked(8192):
                        # 检查客户端连接是否仍然活跃，无法避免客户端异常关闭，只能捕获异常并忽略
                        if request.transport is None or request.transport.is_closing():
                            # 客户端连接已关闭，停止传输数据
                            break
                        try:
                            await stream_response.write(chunk)
                        except Exception as e:
                            # 客户端可能在我们发送数据时断开了连接
                            break
                    try:
                        await stream_response.write_eof()
                    except Exception as e:
                        # 客户端可能在我们发送EOF时断开了连接
                        pass
                    return stream_response
        except Exception as e:
            logger.error(f"Proxy error: {str(e)}")
            return web.Response(status=500, text=str(e))

    def _get_target_url(
        self, request: web.Request, target: str = None
    ) -> Optional[str]:
        """获取目标URL"""
        target = target or self.config.get("target")
        if not target:
            return None
        # 构建完整URL
        url = f"{target.rstrip('/')}{request.rel_url}"
        return url

    def _prepare_headers(self, request: web.Request, custom_headers: Dict = {}) -> Dict:
        """准备请求头"""
        headers = dict(request.headers)
        # 移除一些不需要转发的头
        headers.pop("Host", None)
        headers.pop("Content-Length", None)
        # 添加访问者真实IP
        headers["X-Real-IP"] = request.remote
        # 添加自定义头
        headers.update(custom_headers)
        return headers

    async def _prepare_body(self, request: web.Request) -> Optional[bytes]:
        """准备请求体"""
        # if request.method in ("GET", "HEAD", "OPTIONS"):
        #     return None
        return await request.read()

    def _prepare_response_headers(self, response: aiohttp.ClientResponse) -> Dict:
        """准备响应头"""
        headers = dict(response.headers)
        # 移除一些不需要转发的头
        headers.pop("Transfer-Encoding", None)
        headers.pop("Content-Encoding", None)
        headers.pop("Content-Length", None)
        return headers

    def _get_target_title(self):
        """获取目标标题"""
        target = self.config.get("target")
        if not target:
            return None
        try:
            import re
            import requests

            response = requests.get(target, timeout=5)
            if response.status_code == 200:
                if title_match := re.search(
                    r"<title[^>]*>(.*?)</title>",
                    response.text,
                    re.IGNORECASE | re.DOTALL,
                ):
                    return title_match.group(1).strip()
            return None
        except Exception as e:
            return None

    def _is_websocket_request(self, request: web.Request) -> bool:
        """检测是否为WebSocket请求"""
        headers = request.headers
        return (
            request.method == "GET"
            and "upgrade" in headers.get("Connection", "").lower()
            and headers.get("Upgrade", "").lower() == "websocket"
            and headers.get("Sec-WebSocket-Version") == "13"
            and bool(headers.get("Sec-WebSocket-Key"))
        )

    async def _handle_websocket(
        self, request: web.Request, target: str = None
    ) -> web.Response:
        """处理WebSocket代理转发"""
        try:
            # 获取目标WebSocket URL
            target_url = self._get_target_url(request, target)
            if not target_url:
                return web.Response(status=404, text="No target URL configured")
            # 将HTTP URL转换为WebSocket URL
            ws_target_url = target_url.replace("http://", "ws://").replace(
                "https://", "wss://"
            )
            logger.debug(f"建立WebSocket代理连接: {request.path} -> {ws_target_url}")
            # 准备客户端WebSocket连接的头部
            client_headers = self._prepare_websocket_headers(request)
            # 创建服务器端WebSocket响应
            ws_server = web.WebSocketResponse()
            await ws_server.prepare(request)
            # 建立到目标服务器的WebSocket连接
            async with aiohttp.ClientSession() as session:
                try:
                    async with session.ws_connect(
                        ws_target_url,
                        headers=client_headers,
                        protocols=(
                            request.headers.get("Sec-WebSocket-Protocol", "").split(",")
                            if request.headers.get("Sec-WebSocket-Protocol")
                            else None
                        ),
                    ) as ws_client:
                        # 创建双向消息转发任务
                        await asyncio.gather(
                            self._forward_websocket_messages(
                                ws_server, ws_client, "client->server"
                            ),
                            self._forward_websocket_messages(
                                ws_client, ws_server, "server->client"
                            ),
                            return_exceptions=True,
                        )
                except Exception as e:
                    logger.debug(f"WebSocket客户端连接失败: {e}")
                    if not ws_server.closed:
                        await ws_server.close(
                            code=aiohttp.WSCloseCode.INTERNAL_ERROR,
                            message=f"Target connection failed: {str(e)}".encode(),
                        )
            return ws_server
        except Exception as e:
            logger.debug(f"WebSocket代理错误: {e}")
            return web.Response(status=500, text=f"WebSocket proxy error: {str(e)}")

    def _prepare_websocket_headers(self, request: web.Request) -> Dict:
        """准备WebSocket客户端连接的头部"""
        headers = {}
        # 复制相关的头部，但排除一些不需要的
        for name, value in request.headers.items():
            name_lower = name.lower()
            if name_lower not in [
                "host",
                "connection",
                "upgrade",
                "sec-websocket-key",
                "sec-websocket-version",
                "content-length",
                "transfer-encoding",
            ]:
                headers[name] = value
        return headers

    async def _forward_websocket_messages(self, ws_from, ws_to, direction: str):
        """转发WebSocket消息"""

        def _is_websocket_closing(ws) -> bool:
            """检查WebSocket连接是否正在关闭"""
            return ws.closed or (
                hasattr(ws, "_writer")
                and hasattr(ws._writer, "_transport")
                and ws._writer._transport
                and ws._writer._transport.is_closing()
            )

        try:
            async for msg in ws_from:
                # 在转发任何消息之前检查目标连接状态
                if _is_websocket_closing(ws_to):
                    logger.debug(
                        f"WebSocket {direction} 目标连接已关闭或正在关闭，停止转发"
                    )
                    break

                if msg.type == aiohttp.WSMsgType.TEXT:
                    logger.debug(
                        f"WebSocket {direction} 转发文本消息: {len(msg.data)} 字符"
                    )
                    await ws_to.send_str(msg.data)
                elif msg.type == aiohttp.WSMsgType.BINARY:
                    logger.debug(
                        f"WebSocket {direction} 转发二进制消息: {len(msg.data)} 字节"
                    )
                    await ws_to.send_bytes(msg.data)
                elif msg.type == aiohttp.WSMsgType.PING:
                    logger.debug(f"WebSocket {direction} 转发PING")
                    await ws_to.ping(msg.data)
                elif msg.type == aiohttp.WSMsgType.PONG:
                    logger.debug(f"WebSocket {direction} 转发PONG")
                    await ws_to.pong(msg.data)
                elif msg.type == aiohttp.WSMsgType.CLOSE:
                    logger.debug(
                        f"WebSocket {direction} 连接关闭: code={msg.data}, extra={msg.extra}"
                    )
                    if not _is_websocket_closing(ws_to):
                        await ws_to.close(
                            code=msg.data,
                            message=msg.extra.encode() if msg.extra else b"",
                        )
                    break
                elif msg.type == aiohttp.WSMsgType.ERROR:
                    logger.debug(f"WebSocket {direction} 错误: {ws_from.exception()}")
                    break
        except Exception as e:
            logger.debug(f"WebSocket {direction} 消息转发错误: {e}")
            if not _is_websocket_closing(ws_to):
                await ws_to.close(code=aiohttp.WSCloseCode.INTERNAL_ERROR)

    def _run_server(self):
        """运行代理服务器的内部方法"""
        try:
            host = self.config.get("host", "0.0.0.0")
            port = self.config.get("port", 8097)
            # 创建事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            # 创建并启动服务器
            runner = web.AppRunner(self.app)
            loop.run_until_complete(runner.setup())
            site = web.TCPSite(runner, host, port)
            loop.run_until_complete(site.start())
            # 通知主进程服务器已启动
            if self.status_queue:
                self.status_queue.put({"status": "started", "host": host, "port": port})
            try:
                # 运行事件循环
                loop.run_forever()
            except KeyboardInterrupt:
                pass
            finally:
                # 清理资源
                loop.run_until_complete(runner.cleanup())
                loop.close()
                # 通知主进程服务器已停止
                if self.status_queue:
                    self.status_queue.put({"status": "stopped"})
        except Exception as e:
            # logger.error(f"代理服务器运行错误: {e}")
            if self.status_queue:
                self.status_queue.put({"status": "error", "error": str(e)})
            sys.exit(1)

    def _check_port(self, port: int) -> bool:
        """检查端口是否可用"""
        import socket

        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            return s.connect_ex(("localhost", port)) != 0

    def start(self):
        """启动代理服务器"""
        if self.process is not None and self.process.is_alive():
            return False, "代理服务器已经在运行"
        if not self.config.get("target") or not self.config.get("port"):
            return False, "缺少目标地址或端口"
        # 检查端口
        if not self._check_port(self.config.get("port")):
            return False, f"服务端口 {self.config.get('port')} 已被占用，请更换后重试"
        # 创建状态队列
        if not self.status_queue:
            self.status_queue = Queue()
        # 启动代理服务器进程
        self.process = Process(target=self._run_server, daemon=True)
        self.process.start()
        # 等待启动状态
        try:
            status = self.status_queue.get(timeout=5)
            if status.get("status") == "started":
                self.config["title"] = self._get_target_title()
                message = f"代理服务器已启动: http://{status.get('host')}:{status.get('port')}"
                logger.info(message)
                return True, message
            elif status.get("status") == "error":
                message = f"代理服务器启动失败: {status.get('error')}"
                logger.error(message)
                return False, message
        except:
            return False, "启动超时"

    def stop(self):
        """停止代理服务器"""
        if self.process is None or not self.process.is_alive():
            return False, "代理服务器未运行"
        try:
            # 终止进程
            self.process.terminate()
            self.process.join(timeout=5)
            self.process = None
            # 清理状态队列
            if self.status_queue:
                self.status_queue.close()
                self.status_queue = None
            message = f"代理服务器已停止"
            logger.info(message)
            return True, message
        except Exception as e:
            return False, f"停止失败: {str(e)}"

    def is_running(self):
        """检查代理服务器是否正在运行"""
        return self.process is not None and self.process.is_alive()

    def update_config(self, config: dict):
        """更新代理服务器配置"""
        self.config = config
        if self.is_running():
            self.stop()
        if config.get("enabled", False):
            return self.start()
        return True, "配置已更新"
