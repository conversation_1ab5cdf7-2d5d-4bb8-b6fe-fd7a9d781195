import threading
from typing import Dict, Any, Optional
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from utils.file_operation import FileOperationTool
from core.config_manager import ConfigManager
from core.storage_manager import StorageManager
from core.strm_generator import StrmGenerator
from core.log_manager import LogManager

logger = LogManager.get_logger(__name__)


class TaskManager:
    def __init__(self, config_manager: ConfigManager):
        """初始化任务管理器

        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self.storage_manager = StorageManager(config_manager)
        self.scheduler = BackgroundScheduler()
        self.scheduler.start()
        self.tasks: Dict[str, Dict[str, Any]] = {}
        self._load_tasks()

    def _load_tasks(self):
        """加载所有任务"""
        tasks = self.config_manager.get_tasks()
        for task in tasks:
            self.add_task(task["name"], task)

    def add_task(self, name: str, task_config: Dict[str, Any]) -> bool:
        """添加任务

        Args:
            name: 任务名称
            task_config: 任务配置

        Returns:
            bool: 是否添加成功
        """
        try:
            # 保存任务信息
            self.tasks[name] = {
                "config": task_config,
            }

            # 添加定时任务
            if task_config.get("crontab"):
                self.scheduler.add_job(
                    self.run_task,
                    CronTrigger.from_crontab(task_config["crontab"]),
                    args=[name],
                    id=name,
                    replace_existing=True,
                )
                logger.info(f"添加定时任务: {name}, crontab: {task_config['crontab']}")

            return True
        except Exception as e:
            logger.error(f"添加任务失败: {e}")
            return False

    def update_task(self, name: str, task_config: Dict[str, Any]) -> bool:
        """更新任务

        Args:
            name: 任务名称
            task_config: 任务配置

        Returns:
            bool: 是否更新成功
        """
        try:
            logger.info(f"更新任务: {name}, 新任务配置: {task_config}")

            # 移除取消定时的任务
            if self.scheduler.get_job(name) and not task_config.get("crontab"):
                self.scheduler.remove_job(name)

            # 添加/更新任务
            return self.add_task(name, task_config)
        except Exception as e:
            logger.error(f"更新任务失败: {e}")
            return False

    def delete_task(self, name: str) -> bool:
        """删除任务

        Args:
            name: 任务名称

        Returns:
            bool: 是否删除成功
        """
        try:
            # 删除定时任务
            if name in self.tasks and self.tasks[name]["config"].get("crontab"):
                self.scheduler.remove_job(name)

            # 删除任务信息
            if name in self.tasks:
                del self.tasks[name]

            return True
        except Exception as e:
            logger.error(f"删除任务失败: {e}")
            return False

    def run_task(self, name: str) -> bool:
        """运行任务

        Args:
            name: 任务名称

        Returns:
            bool: 是否运行成功
        """
        try:
            if name not in self.tasks:
                logger.error(f"任务不存在: {name}")
                return False

            task = self.tasks[name]
            config = task["config"]

            # 创建存储实例
            storage = self.storage_manager.get_storage(config["storage"])
            if not storage:
                logger.error(f"创建存储实例失败: {config['storage']}")
                return False

            # 获取 STRM 配置
            strm_config = self.config_manager.get_settings()["strm"]

            # 创建 STRM 生成器
            generator = StrmGenerator(
                storage=storage,
                task_config=config,
                strm_config=strm_config,
            )

            # 运行任务
            generator.handle_path(config["storage_path"])
            return True
        except Exception as e:
            logger.error(f"运行任务失败: {e}")
            return False

    def run_task_async(self, name: str) -> bool:
        """异步运行任务

        Args:
            name: 任务名称

        Returns:
            bool: 是否启动成功
        """
        try:
            thread = threading.Thread(target=self.run_task, args=(name,))
            thread.daemon = True
            thread.start()
            return True
        except Exception as e:
            logger.error(f"启动异步任务失败: {e}")
            return False

    def run_task_tools(self, name: str, data: Dict[str, Any]) -> bool:
        """允许内容替换任务

        Args:
            name: 任务名称
            data: 替换参数
                find_text: 要查找的旧文本
                replace_text: 用来替换新文本
        Returns:
            bool: 是否启动成功
        """
        try:
            if name not in self.tasks:
                logger.error(f"任务不存在: {name}")
                return False
            task = self.tasks[name]
            task_config = task["config"]

            # 获取 STRM 配置
            strm_config = self.config_manager.get_settings()["strm"]

            # 创建文件操作工具
            file_tool = FileOperationTool(task_config, strm_config)

            tool = data.get("tool")
            if tool == "content_replace":
                file_tool.replace_content_in_files(
                    f"{strm_config['save_dir']}/{task_config['name']}",
                    data.get("file_ext", ".strm"),
                    data["find_text"],
                    data["replace_text"],
                )
                return True
            elif tool == "clear_task_dir":
                file_tool.clear_directory(
                    f"{strm_config['save_dir']}/{task_config['name']}"
                )
                return True
            return False
        except Exception as e:
            logger.error(f"启动内容替换任务失败: {e}")
            return False

    def get_task_status(self, name: str) -> Dict[str, Any]:
        """获取任务状态

        Args:
            name: 任务名称

        Returns:
            Dict: 任务状态信息
        """
        try:
            if name not in self.tasks:
                return {"error": "任务不存在"}

            task = self.tasks[name]
            job = self.scheduler.get_job(name)

            return {
                "name": name,
                "config": task["config"],
                "next_run": (
                    job.next_run_time.strftime("%Y-%m-%d %H:%M") if job else None
                ),
            }
        except Exception as e:
            logger.error(f"获取任务状态失败: {e}")
            return {"error": str(e)}

    def get_all_tasks_status(self) -> Dict[str, Dict[str, Any]]:
        """获取所有任务状态

        Returns:
            Dict: 所有任务状态信息
        """
        return {name: self.get_task_status(name) for name in self.tasks}

    def reload_tasks(self):
        """重载所有任务"""
        try:
            # 清除所有现有任务
            for name in list(self.tasks.keys()):
                self.delete_task(name)

            # 重新加载所有任务
            self._load_tasks()
            logger.info("所有任务已重载")
            return True
        except Exception as e:
            logger.error(f"重载任务失败: {e}")
            return False
