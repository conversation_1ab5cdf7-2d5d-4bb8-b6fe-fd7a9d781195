import re
import time
import json
import hashlib
import hmac
import base64
from flask import Request, Response, redirect
import requests
import datetime
from urllib.parse import unquote
from Crypto.PublicKey import RSA
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
from Crypto.Cipher import PKCS1_v1_5
from ._base import BaseDriver
from utils.kv_cache import K<PERSON>ache
from core.log_manager import LogManager
from core.config_manager import Config<PERSON>anager
from typing import Dict, Any, List, Optional

logger = LogManager.get_logger(__name__)

# 天翼云盘API常量
WEB_URL = "https://cloud.189.cn"
API_URL = "https://api.cloud.189.cn"
AUTH_URL = "https://open.e.189.cn"

# 新认证方式常量
AES_IV = "Zx8dG46ax3Mc8Mj2".encode()  # 16 字节
AES_KEY = "bf8395f745c04f23".encode()  # 16 字节
RSA_PUBLIC_KEY_PEM = """-----B<PERSON>IN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDC72L803mNmrQgyvaU
t115S5gSHuDcS+nGdqBakHYqFShEwrEaqKsr2Z/7DQt9AobB0ne2vIS
UW0tXjhgf5vfl00kT7K+J4j+t3WLkQ6Zwc9KtZHkSW6/fkFSC1EnShP
YLsG6rHYa5+wfefOY2P7yEFRsd5DGCqHNWkzOZclsXawIDAQAB
-----END PUBLIC KEY-----"""

# 客户端配置
ACCOUNT_TYPE = "02"
APP_ID = "**********"
CLIENT_TYPE = "10020"
RETURN_URL = "https://m.cloud.189.cn/zhuanti/2020/loginErrorPc/index.html"
VERSION = "6.2"
PC = "TELEPC"
CID = "web_cloud.189.cn"
USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36"


# 新认证相关的加密函数
def aes_cbc_encrypt(plaintext, key, iv):
    """AES CBC 加密函数"""
    cipher = AES.new(key, AES.MODE_CBC, iv)
    padded_plaintext = pad(plaintext.encode('utf-8'), AES.block_size)
    ciphertext = cipher.encrypt(padded_plaintext)
    return base64.b64encode(ciphertext).decode('utf-8')


def aes_cbc_decrypt(ciphertext, key, iv):
    """AES CBC 解密函数"""
    ciphertext = base64.b64decode(ciphertext)
    cipher = AES.new(key, AES.MODE_CBC, iv)
    padded_plaintext = cipher.decrypt(ciphertext)
    plaintext = unpad(padded_plaintext, AES.block_size)
    return plaintext.decode('utf-8')


def encrypt_aes_key(key):
    """RSA加密AES密钥"""
    aes_session_key = key.encode('utf-8')
    rsa_public_key = RSA.import_key(RSA_PUBLIC_KEY_PEM)
    rsa_cipher = PKCS1_v1_5.new(rsa_public_key)
    encrypted_aes_key = rsa_cipher.encrypt(aes_session_key)
    return base64.b64encode(encrypted_aes_key).decode('utf-8')


def md5_encrypt(plaintext):
    """MD5加密"""
    md5_hash = hashlib.md5()
    md5_hash.update(plaintext.encode('utf-8'))
    return md5_hash.hexdigest()


def hmac_sha1_hex(key, message):
    """HMAC SHA1签名"""
    key_bytes = key.encode('utf-8')
    message_bytes = message.encode('utf-8')
    hmac_hash = hmac.new(key_bytes, message_bytes, hashlib.sha1)
    return hmac_hash.digest().hex()


def generate_rsa_key():
    """生成RSA密钥"""
    headers = {
        'Host': 'api.cloud.189.cn',
        'accept': 'application/json;charset=UTF-8',
        'accept-language': 'zh-cn',
        'x-request-id': '26E11B6CBEB94F77A2E5615C20C06113',
        'date': 'Sun, 22 Jun 2025 14:33:08 GMT',
        'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_8 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko)  Mobile/15E148 Safari/604.1',
    }

    params = {
        'clientType': 'TELEIPHONE',
        'version': '10.3.3',
        'model': 'iPhone',
        'osFamily': 'iOS',
        'osVersion': '15.8.3',
        'clientSn': '02676BE3DD-8D86-4B4F-A666-749D1D5C9FF8',
        'returnType': 'JSON',
    }

    response = requests.get('https://api.cloud.189.cn/security/generateRsaKey.action', params=params, headers=headers).json()
    return response['pkId']


class Cloud189Driver(BaseDriver):
    """天翼云盘驱动类"""

    DRIVER_TYPE = "cloud189"
    DRIVER_NAME = "天翼云盘"

    DRIVER_CONFIG = {
        "access_token": {
            "type": "string",
            "required": True,
            "label": "AccessToken",
            "tip": "从天翼云盘APP抓取的accessToken，一般在请求头里",
        },
        "session_key": {
            "type": "hidden",
            "required": False,
            "label": "",
            "tip": "",
        },
        "session_secret": {
            "type": "hidden",
            "required": False,
            "label": "",
            "tip": "",
        },
        "family_session_key": {
            "type": "hidden",
            "required": False,
            "label": "",
            "tip": "",
        },
        "login_name": {
            "type": "hidden",
            "required": False,
            "label": "",
            "tip": "",
        },
        "user_info": {
            "type": "hidden",
            "required": False,
            "label": "",
            "tip": "",
        },
    }

    DRIVER_TIPS = {
        "type": "info",
        "message": "使用天翼云盘新认证API访问<br><b>注意：需要从天翼云盘APP抓取accessToken，打开天翼云盘APP抓请求url=api.cloud.189.cn/guns/getPageBanners.action里面accessToken(一般在请求头里)填入即可。</b>",
    }

    def __init__(self, config: Dict[str, Any]):
        """初始化天翼云盘驱动

        Args:
            config: 驱动配置
        """
        self.name = None
        self.access_token = config["access_token"].strip()
        self.strm_mode = config.get("strm_mode", "direct")
        # 认证信息
        self.session_key = config.get("session_key", "")
        self.session_secret = config.get("session_secret", "")
        self.family_session_key = config.get("family_session_key", "")
        self.login_name = config.get("login_name", "")
        # 缓存
        self.fid_cache = KVCache()
        self.fid_cache.set("/", "-11", None)  # 天翼云盘根目录ID为-11
        self.download_cache = KVCache()
        # 配置管理器
        self.config_manager = ConfigManager
        # 用户信息
        self.user_info = {}
        # 请求会话
        self.session = requests.Session()
        self.session.headers.update(
            {
                "User-Agent": USER_AGENT,
                "Referer": f"{WEB_URL}/web/main/",
                "Accept": "application/json;charset=UTF-8",
            }
        )

    def _save_tokens(self):
        """保存认证信息到配置"""
        try:
            config = self.config_manager.get_storage(self.name)
            if config:
                config.update(
                    {
                        "session_key": self.session_key,
                        "session_secret": self.session_secret,
                        "family_session_key": self.family_session_key,
                        "login_name": self.login_name,
                    }
                )
                return self.config_manager.update_storage(self.name, config)
            return False
        except Exception as e:
            logger.error(f"保存认证信息失败: {e}")
            return False

    def login4_merged_client(self, token):
        """使用新的认证方式登录"""
        timestamp = str(int(time.time()))
        appsignature = hmac_sha1_hex('fe5734c74c2f96a38157f420b32dc995',
                                     f'AppKey=600100885&Operate=POST&RequestURI=/login4MergedClient.action&Timestamp={timestamp}')
        param = f'isCache=1&jgOpenId=1114a89792bbaa8d350&deviceModel=iPhone%206s%20Plus&exRetryTimes=1&accessToken={token}&networkAccessMode=WIFI&telecomsOperator=&idfa=00000000-0000-0000-0000-000000000000&clientType=TELEIPHONE&version=10.3.3&model=iPhone&osFamily=iOS&osVersion=15.8.3&clientSn=02676BE3DD-8D86-4B4F-A666-749D1D5C9FF8'
        encrypted_param = aes_cbc_encrypt(param, AES_KEY, AES_IV)
        epkey = encrypt_aes_key("bf8395f745c04f23")
        pkId = generate_rsa_key()
        headers = {
            'Host': 'api.cloud.189.cn',
            'content-type': 'application/x-www-form-urlencoded',
            'epver': '2',
            'accept': '*/*',
            'epway': '3',
            'timestamp': timestamp,
            'appsignature': appsignature,
            'accept-language': 'zh-CN,zh-Hans;q=0.9',
            'x-request-id': '26E11B6CBEB94F77A2E5615C20C06113',
            'epkey': epkey,
            'appkey': '600100885',
            'user-agent': 'Cloud189/8 CFNetwork/1410.0.3 Darwin/22.6.0',
        }

        data = {
            'pkId': pkId,
            'param': encrypted_param,
        }

        try:
            response = requests.post('https://api.cloud.189.cn/login4MergedClient.action', headers=headers, data=data).text
            ciphertext = re.search(r'<ciphertext>(.*?)</ciphertext>', response, re.DOTALL).group(1)
            user_data = unquote(aes_cbc_decrypt(ciphertext, AES_KEY, AES_IV))
            # 解析用户数据
            login_name = re.search(r'<loginName>(.*?)</loginName>', user_data, re.DOTALL).group(1)
            session_key = re.search(r'<sessionKey>(.*?)</sessionKey>', user_data, re.DOTALL).group(1)
            session_secret = re.search(r'<sessionSecret>(.*?)</sessionSecret>', user_data, re.DOTALL).group(1)
            family_session_key = re.search(r'<familySessionKey>(.*?)</familySessionKey>', user_data, re.DOTALL).group(1)

            # 保存认证信息
            self.login_name = login_name
            self.session_key = session_key
            self.session_secret = session_secret
            self.family_session_key = family_session_key
            self._save_tokens()

            return True
        except Exception as e:
            logger.error(f"新认证方式登录失败: {e}")
            return False

    def _convert_time(self, time_str: str) -> int:
        """
        解析 'YYYY-MM-DD HH:MM:SS' 格式的字符串为时间戳字符串。
        如果输入格式不匹配，则原样返回。
        """
        try:
            dt_object = datetime.datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
            timestamp = int(dt_object.timestamp())
            return timestamp
        except ValueError:
            return 0
        except Exception:
            return 0

    def _ensure_login(self) -> bool:
        """确保已登录"""
        try:
            # 如果有session_key，先尝试使用
            if self.session_key and self.session_secret:
                if self._test_session():
                    logger.debug("使用已有 session_key 登录成功")
                    return True
            # 使用access_token进行新认证方式登录
            if self.access_token:
                if self.login4_merged_client(self.access_token):
                    logger.debug("使用新认证方式登录成功")
                    return True
            logger.error("登录失败：缺少有效的认证信息")
            return False
        except Exception as e:
            logger.error(f"登录失败: {e}")
            return False

    def _test_session(self) -> bool:
        """测试session_key是否有效"""
        try:
            # 使用新认证方式的签名验证
            gmt_time = datetime.datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S GMT')
            signature = hmac_sha1_hex(self.session_secret,
                                      f'SessionKey={self.session_key}&Operate=GET&RequestURI=/api/portal/getUserSizeInfo.action&Date={gmt_time}')

            headers = {
                'Host': 'cloud.189.cn',
                'signature': signature,
                'date': gmt_time,
                'sessionkey': self.session_key,
                'accept': 'application/json;charset=UTF-8',
                'user-agent': USER_AGENT,
            }

            url = f"{WEB_URL}/api/portal/getUserSizeInfo.action"
            response = self.session.get(url, headers=headers)
            if response.status_code == 200:
                data = response.json()
                return data.get("res_code") == 0
            return False
        except Exception as e:
            logger.error(f"测试session失败: {e}")
            return False



    def _get_user_info(self) -> Dict[str, Any]:
        """获取磁盘空间信息"""
        try:
            # 使用新认证方式的签名
            gmt_time = datetime.datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S GMT')
            signature = hmac_sha1_hex(self.session_secret,
                                      f'SessionKey={self.session_key}&Operate=GET&RequestURI=/api/open/user/getUserInfoForPortal.action&Date={gmt_time}')

            headers = {
                'Host': 'cloud.189.cn',
                'signature': signature,
                'date': gmt_time,
                'sessionkey': self.session_key,
                'accept': 'application/json;charset=UTF-8',
                'user-agent': USER_AGENT,
            }

            url = f"{WEB_URL}/api/open/user/getUserInfoForPortal.action"
            response = self.session.get(url, headers=headers)
            data = response.json()
        except Exception as e:
            logger.error(f"获取空间信息失败: {e}")
            data = {}
        user_info = {
            "user_name": data.get("userExtResp", {}).get("nickName", ""),
            "user_face": data.get("userExtResp", {}).get("headPortraitUrl3", ""),
            "vip_level": "",
            "space_info": {
                "total": data.get("capacity", 0),
                "remain": data.get("available", 0),
                "use": (data.get("capacity", 0) - data.get("available", 0)),
            },
        }
        return user_info

    def init_info(self) -> bool:
        """初始化用户信息"""
        # 确保已登录
        if not self._ensure_login():
            return False
        self.user_info = self._get_user_info()
        # 保存用户信息到配置
        config = self.config_manager.get_storage(self.name)
        config["user_info"] = self.user_info
        self.config_manager.update_storage(self.name, config)
        return True

    def _get_fid_by_path(self, path: str) -> str:
        """获取文件夹ID"""
        # 检查缓存
        if fid := self.fid_cache.get(path):
            # logger.debug(f"使用缓存: {path}->{fid}")
            return fid
        # 分解路径
        parts = [p for p in path.split("/") if p]
        fid = ""
        pfid = "-11"
        current_path = "/"
        try:
            for part in parts:
                next_path = f"{current_path}/{part}".replace("//", "/")
                # 检查缓存
                if fid := self.fid_cache.get(next_path):
                    # 已有缓存跳下一层
                    current_path = next_path
                    pfid = fid
                    continue
                # 获取当前目录下的文件列表
                # logger.debug(f"读取路径: {next_path}，在pfid: {pfid}")
                files = self._get_all_files(pfid)
                # 查找目标文件
                for file_info in files:
                    file_path = f"{current_path}/{file_info['name']}".replace("//", "/")
                    self.fid_cache.set(file_path, file_info["id"], 60 * 10)
                    if file_path == next_path:
                        current_path = next_path
                        pfid = file_info["id"]
                    # logger.debug(f"  检查文件: {file_path}  {next_path}  {path}")
                    if file_path == path:
                        fid = file_info["id"]
            if fid:
                return fid
            raise FileNotFoundError(f"文件不存在: {current_path}")
        except Exception as e:
            logger.error(f"获取文件ID失败: {e}")
            raise

    def _get_all_files(self, folder_id: str) -> List[Dict[str, Any]]:
        """获取文件夹下的所有文件"""
        try:
            files = []
            page_num = 1
            while True:
                # 使用新认证方式的签名
                gmt_time = datetime.datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S GMT')
                signature = hmac_sha1_hex(self.session_secret,
                                          f'SessionKey={self.session_key}&Operate=GET&RequestURI=/api/open/file/listFiles.action&Date={gmt_time}')

                headers = {
                    'Host': 'cloud.189.cn',
                    'signature': signature,
                    'date': gmt_time,
                    'sessionkey': self.session_key,
                    'accept': 'application/json;charset=UTF-8',
                    'user-agent': USER_AGENT,
                }

                url = f"{WEB_URL}/api/open/file/listFiles.action"
                params = {
                    "pageSize": "60",
                    "pageNum": str(page_num),
                    "mediaType": "0",
                    "folderId": str(folder_id),
                    "iconOption": "5",
                    "orderBy": "lastOpTime",
                    "descending": "true",
                }
                response = self.session.get(url, params=params, headers=headers)
                data = response.json()
                if data.get("res_code") != 0:
                    break
                file_list = data.get("fileListAO", {})
                # 处理文件夹
                for f in file_list.get("folderList", []):
                    files.append(
                        {
                            "id": f["id"],
                            "name": f["name"],
                            "modified": self._convert_time(f["lastOpTime"]),
                            "created": self._convert_time(f["createDate"]),
                            "isdir": True,
                        }
                    )
                # 处理文件
                for f in file_list.get("fileList", []):
                    files.append(
                        {
                            "id": f["id"],
                            "name": f["name"],
                            "modified": self._convert_time(f["lastOpTime"]),
                            "created": self._convert_time(f["createDate"]),
                            "size": f.get("size", ""),
                            "isdir": False,
                            "type": f.get("mediaType", ""),
                        }
                    )
                if len(files) >= file_list.get("count", 0):
                    break
                page_num += 1
            return files
        except Exception as e:
            logger.error(f"获取文件列表失败: {e}")
            return []

    def list_files(self, path: str) -> Dict[str, Any]:
        """列出指定路径下的所有文件"""
        try:
            # 获取文件夹ID
            folder_id = self._get_fid_by_path(path)
            # 获取文件列表
            files = self._get_all_files(folder_id)
            # 格式化文件信息
            formatted_files = []
            for file_info in files:
                formatted_file = {
                    "name": file_info.get("name", ""),
                    "isdir": file_info.get("isdir", False),
                    "path": f"{path.rstrip('/')}/{file_info.get('name', '')}",
                    "size": file_info.get("size", ""),
                    "modified": file_info.get("modified", ""),
                    "created": file_info.get("created", ""),
                    # 可选参数
                    "id": file_info.get("id", ""),
                }
                formatted_files.append(formatted_file)
            return {"success": True, "data": formatted_files}
        except Exception as e:
            logger.error(f"列出文件失败: {e}")
            return {"success": False, "message": f"列出文件失败: {str(e)}"}

    def delete_file(self, path: str) -> bool:
        """删除指定文件"""
        try:
            # 分解路径
            parent_path = "/".join(path.split("/")[:-1]) or "/"
            file_name = path.split("/")[-1]
            # 获取父文件夹ID
            parent_id = self._get_fid_by_path(parent_path)
            # 获取文件列表，找到要删除的文件
            files = self._get_all_files(parent_id)
            target_file = None
            for file_info in files:
                if file_info.get("name") == file_name:
                    target_file = file_info
                    break
            if not target_file:
                logger.error(f"文件不存在: {path}")
                return False
            # 删除文件
            file_id = target_file.get("id")
            isdir = target_file.get("isdir", False)
            task_infos = [
                {
                    "fileId": file_id,
                    "fileName": file_name,
                    "isFolder": int(isdir),
                }
            ]

            # 使用新认证方式的签名
            gmt_time = datetime.datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S GMT')
            signature = hmac_sha1_hex(self.session_secret,
                                      f'SessionKey={self.session_key}&Operate=POST&RequestURI=/api/open/batch/createBatchTask.action&Date={gmt_time}')

            headers = {
                'Host': 'cloud.189.cn',
                'signature': signature,
                'date': gmt_time,
                'sessionkey': self.session_key,
                'accept': 'application/json;charset=UTF-8',
                'user-agent': USER_AGENT,
                'Content-Type': 'application/x-www-form-urlencoded',
            }

            data = {
                "type": "DELETE",
                "targetFolderId": "",
                "taskInfos": json.dumps(task_infos),
            }
            url = f"{WEB_URL}/api/open/batch/createBatchTask.action"
            response = self.session.post(url, data=data, headers=headers)
            result = response.json()
            if result.get("res_code") == 0:
                # 清除缓存
                if isdir:
                    self.fid_cache.delete(path)
                logger.info(f"删除文件成功: {path}")
                return True
            else:
                logger.error(f"删除文件失败: {result}")
                return False
        except Exception as e:
            logger.error(f"删除文件失败: {e}")
            return False

    def rename_file(self, path: str, new_name: str) -> bool:
        """重命名文件或目录"""
        try:
            # 获取文件信息
            file_info = self._get_file_info(path)
            if not file_info:
                logger.error(f"文件不存在: {path}")
                return False
            file_id = file_info.get("id")
            isdir = file_info.get("isdir", False)

            # 使用新认证方式的签名
            gmt_time = datetime.datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S GMT')

            if isdir:
                # 重命名文件夹
                url = f"{WEB_URL}/api/open/file/renameFolder.action"
                data = {"folderId": file_id, "destFolderName": new_name}
                signature = hmac_sha1_hex(self.session_secret,
                                          f'SessionKey={self.session_key}&Operate=POST&RequestURI=/api/open/file/renameFolder.action&Date={gmt_time}')
            else:
                # 重命名文件
                url = f"{WEB_URL}/api/open/file/renameFile.action"
                data = {"fileId": file_id, "destFileName": new_name}
                signature = hmac_sha1_hex(self.session_secret,
                                          f'SessionKey={self.session_key}&Operate=POST&RequestURI=/api/open/file/renameFile.action&Date={gmt_time}')

            headers = {
                'Host': 'cloud.189.cn',
                'signature': signature,
                'date': gmt_time,
                'sessionkey': self.session_key,
                'accept': 'application/json;charset=UTF-8',
                'user-agent': USER_AGENT,
                'Content-Type': 'application/x-www-form-urlencoded',
            }

            # 发送重命名请求
            response = self.session.post(url, data=data, headers=headers)
            result = response.json()
            if result.get("res_code") == 0:
                logger.info(f"重命名文件成功: {path} -> {new_name}")
                # 清除相关缓存
                self.fid_cache.delete(path)
                self.download_cache.delete(path)
                return True
            else:
                logger.error(f"重命名文件失败: {result.get('message', result)}")
                return False
        except Exception as e:
            logger.error(f"重命名文件失败: {e}")
            return False

    def get_download_url(self, path: str, file_info: Dict[str, Any] = {}) -> str:
        """获取下载 URL"""
        try:
            # 检查缓存
            cached_url = self.download_cache.get(path)
            if cached_url:
                return cached_url
            file_id = self._get_fid_by_path(path)
            if not file_id:
                raise ValueError("文件ID不存在")

            # 使用新认证方式的签名
            gmt_time = datetime.datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S GMT')
            signature = hmac_sha1_hex(self.session_secret,
                                      f'SessionKey={self.session_key}&Operate=GET&RequestURI=/api/open/file/getFileDownloadUrl.action&Date={gmt_time}')

            headers = {
                'Host': 'cloud.189.cn',
                'signature': signature,
                'date': gmt_time,
                'sessionkey': self.session_key,
                'accept': 'application/json;charset=UTF-8',
                'user-agent': USER_AGENT,
            }

            # 获取下载链接
            url = f"{WEB_URL}/api/open/file/getFileDownloadUrl.action"
            params = {
                "clientType": CLIENT_TYPE,
                "version": VERSION,
                "channelId": CID,
                "fileId": file_id,
            }
            response = self.session.get(url, params=params, headers=headers)
            data = response.json()
            if data.get("res_code") == 0:
                download_url = data.get("fileDownloadUrl", "")
                # 缓存下载链接（5分钟）
                self.download_cache.set(path, download_url, 300)
                return download_url
            else:
                raise Exception(f"获取下载链接失败: {data}")
        except Exception as e:
            logger.error(f"获取下载URL失败: {e}")
            return ""

    def _get_video_url(self, path: str, file_info: Dict[str, Any] = {}) -> str:
        """获取视频转码直链

        Args:
            path: 文件路径
            file_info: 文件信息

        Returns:
            str: 视频转码直链
        """
        try:
            file_id = self._get_fid_by_path(path)
            if not file_id:
                raise ValueError("文件ID不存在")

            # 使用新认证方式的签名
            gmt_time = datetime.datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S GMT')
            signature = hmac_sha1_hex(self.session_secret,
                                      f'SessionKey={self.session_key}&Operate=GET&RequestURI=/api/portal/getNewVlcVideoPlayUrl.action&Date={gmt_time}')

            headers = {
                'Host': 'cloud.189.cn',
                'signature': signature,
                'date': gmt_time,
                'sessionkey': self.session_key,
                'accept': 'application/json;charset=UTF-8',
                'user-agent': USER_AGENT,
            }

            url = f"{WEB_URL}/api/portal/getNewVlcVideoPlayUrl.action"
            params = {
                "fileId": file_id,
                "type": "2",
            }
            response = self.session.get(url, params=params, headers=headers)
            data = response.json()
            if data.get("res_code") == 0:
                return data["normal"]["url"]
        except Exception as e:
            logger.error(f"获取视频直链失败: {e}")
            return ""

    def get_strm_url(self, path: str, file_info: Dict[str, Any] = {}) -> str:
        """获取 strm URL"""
        return f"SMARTSTRM_BASE/smartstrm/{self.name}/{path}".replace("//", "/")

    def handle_strm_request(self, request: Request, file_path: str) -> Response:
        """处理 STRM 请求"""
        video_url = self._get_video_url(file_path)
        if not video_url:
            return Response("Video URL not found", 404)
        response = redirect(video_url, code=302)
        return response

    def get_file_data(self, path: str, file_info: Dict[str, Any] = {}) -> bytes:
        """获取文件二进制数据"""
        try:
            # 获取下载链接
            download_url = self.get_download_url(path, file_info)
            if not download_url:
                raise ValueError("无法获取下载链接")

            # 下载文件数据
            response = requests.get(download_url, timeout=30)
            response.raise_for_status()

            return response.content

        except Exception as e:
            logger.error(f"获取文件数据失败: {e}")
            return b""

    def _get_file_info(self, path: str) -> Optional[Dict[str, Any]]:
        """获取文件信息"""
        try:
            # 分解路径
            parent_path = "/".join(path.split("/")[:-1]) or "/"
            file_name = path.split("/")[-1]
            # 获取父文件夹ID
            parent_id = self._get_fid_by_path(parent_path)
            # 获取文件列表，找到目标文件
            files = self._get_all_files(parent_id)
            for file_info in files:
                if file_info.get("name") == file_name:
                    return file_info
            return None
        except Exception as e:
            logger.error(f"获取文件信息失败: {e}")
            return None
