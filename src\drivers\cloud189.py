import re
import time
import json
import hashlib
from flask import Request, Response, redirect
import requests
import datetime
from ._base import BaseDriver
from utils.kv_cache import KVCache
from core.log_manager import LogManager
from core.config_manager import ConfigManager
from typing import Dict, Any, List, Optional

logger = LogManager.get_logger(__name__)

# 天翼云盘API常量
WEB_URL = "https://cloud.189.cn"
API_URL = "https://api.cloud.189.cn"
AUTH_URL = "https://open.e.189.cn"

# 客户端配置
ACCOUNT_TYPE = "02"
APP_ID = "**********"
CLIENT_TYPE = "10020"
RETURN_URL = "https://m.cloud.189.cn/zhuanti/2020/loginErrorPc/index.html"
VERSION = "6.2"
PC = "TELEPC"
CID = "web_cloud.189.cn"
USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36"


class Cloud189Driver(BaseDriver):
    """天翼云盘驱动类"""

    DRIVER_TYPE = "cloud189"
    DRIVER_NAME = "天翼云盘"

    DRIVER_CONFIG = {
        "username": {
            "type": "string",
            "required": True,
            "label": "手机号",
            "tip": "",
        },
        "password": {
            "type": "password",
            "required": True,
            "label": "密码",
            "tip": "",
        },
        "access_token": {
            "type": "hidden",
            "required": False,
            "label": "",
            "tip": "",
        },
        "refresh_token": {
            "type": "hidden",
            "required": False,
            "label": "",
            "tip": "",
        },
        "session_key": {
            "type": "hidden",
            "required": False,
            "label": "",
            "tip": "",
        },
        "user_info": {
            "type": "hidden",
            "required": False,
            "label": "",
            "tip": "",
        },
    }

    DRIVER_TIPS = {
        "type": "info",
        "message": "使用天翼云盘API访问<br><b>注意：登录状态会自动维护，token失效时会自动重新登录。</b>",
    }

    def __init__(self, config: Dict[str, Any]):
        """初始化天翼云盘驱动

        Args:
            config: 驱动配置
        """
        self.name = None
        self.username = config["username"].strip()
        self.password = config["password"].strip()
        self.strm_mode = config.get("strm_mode", "direct")
        # Token信息
        self.access_token = config.get("access_token", "")
        self.refresh_token = config.get("refresh_token", "")
        self.session_key = config.get("session_key", "")
        # 缓存
        self.fid_cache = KVCache()
        self.fid_cache.set("/", "-11", None)  # 天翼云盘根目录ID为-11
        self.download_cache = KVCache()
        # 配置管理器
        self.config_manager = ConfigManager
        # 用户信息
        self.user_info = {}
        # 请求会话
        self.session = requests.Session()
        self.session.headers.update(
            {
                "User-Agent": USER_AGENT,
                "Referer": f"{WEB_URL}/web/main/",
                "Accept": "application/json;charset=UTF-8",
            }
        )

    def _save_tokens(self):
        """保存token到配置"""
        try:
            config = self.config_manager.get_storage(self.name)
            if config:
                config.update(
                    {
                        "access_token": self.access_token,
                        "refresh_token": self.refresh_token,
                        "session_key": self.session_key,
                    }
                )
                return self.config_manager.update_storage(self.name, config)
            return False
        except Exception as e:
            logger.error(f"保存token失败: {e}")
            return False

    def _get_signature(self, params: Dict[str, Any]) -> str:
        """生成API签名"""
        sorted_params = dict(sorted(params.items()))
        sign_str = "&".join(f"{k}={v}" for k, v in sorted_params.items())
        return hashlib.md5(sign_str.encode()).hexdigest()

    def _convert_time(self, time_str: str) -> int:
        """
        解析 'YYYY-MM-DD HH:MM:SS' 格式的字符串为时间戳字符串。
        如果输入格式不匹配，则原样返回。
        """
        try:
            dt_object = datetime.datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
            timestamp = int(dt_object.timestamp())
            return timestamp
        except ValueError:
            return 0
        except Exception:
            return 0

    def _ensure_login(self) -> bool:
        """确保已登录"""
        try:
            # 如果有session_key，先尝试使用
            if self.session_key:
                if self._test_session():
                    logger.debug("使用 session_key 登录成功")
                    return True
            # 如果有access_token，尝试获取session_key
            if self.access_token:
                if self._get_session_by_access_token():
                    logger.debug("使用 access_token 登录成功")
                    return True
            # 如果有refresh_token，尝试刷新
            if self.refresh_token:
                if self._refresh_access_token():
                    if self._get_session_by_access_token():
                        logger.debug("使用 refresh_token 刷新成功")
                        return True
            # 最后尝试用户名密码登录
            self._login_by_password()
            if self._test_session():
                logger.debug("使用用户名密码登录成功")
                return True
        except Exception as e:
            logger.error(f"登录失败: {e}")
            return False

    def _test_session(self) -> bool:
        """测试session_key是否有效"""
        try:
            url = f"{WEB_URL}/api/portal/getUserSizeInfo.action"
            params = {"sessionKey": self.session_key}
            response = self.session.get(url, params=params)
            if response.status_code == 200:
                # print(response.text)
                data = response.json()
                return data.get("res_code") == 0
            return False
        except:
            return False

    def _get_session_by_access_token(self) -> bool:
        """通过access_token获取session_key"""
        try:
            params = {
                "appId": APP_ID,
                "clientType": PC,
                "version": VERSION,
                "channelId": CID,
                "rand": int(time.time() * 1000),
                "accessToken": self.access_token,
            }
            response = self.session.post(
                f"{API_URL}/getSessionForPC.action", params=params
            )
            data = response.json()
            if data.get("sessionKey"):
                self.session_key = data["sessionKey"]
                self._save_tokens()
                return True
            return False
        except Exception as e:
            logger.error(f"获取session失败: {e}")
            return False

    def _refresh_access_token(self) -> bool:
        """刷新access_token"""
        try:
            data = {
                "clientId": APP_ID,
                "refreshToken": self.refresh_token,
                "grantType": "refresh_token",
                "format": "json",
            }
            response = self.session.post(
                f"{AUTH_URL}/api/oauth2/refreshToken.do", data=data
            )
            result = response.json()
            if result.get("accessToken"):
                self.access_token = result["accessToken"]
                if result.get("refreshToken"):
                    self.refresh_token = result["refreshToken"]
                self._save_tokens()
                return True
            return False
        except Exception as e:
            logger.error(f"刷新token失败: {e}")
            return False

    def _login_by_password(self) -> bool:
        """用户名密码登录"""
        try:
            # 获取加密参数
            # response = self.session.post(f"{AUTH_URL}/api/logbox/config/encryptConf.do")
            # encrypt_conf = response.json().get("data")
            # if not encrypt_conf:
            #     return False
            # 获取登录表单参数
            login_form = self._get_login_form()
            if not login_form:
                return False
            # 构建登录数据
            login_data = {
                "appKey": APP_ID,
                "accountType": ACCOUNT_TYPE,
                "validateCode": "",
                "captchaToken": login_form["captchaToken"],
                "dynamicCheck": "FALSE",
                "clientType": "1",
                "cb_SaveName": "3",
                "isOauth2": "false",
                "returnUrl": RETURN_URL,
                "paramId": login_form["paramId"],
                "userName": self.username,  # 简化处理，实际需要RSA加密
                "password": self.password,  # 简化处理，实际需要RSA加密
            }
            # 提交登录
            headers = {
                "Referer": AUTH_URL,
                "lt": login_form["lt"],
                "REQID": login_form["reqId"],
            }
            response = self.session.post(
                f"{AUTH_URL}/api/logbox/oauth2/loginSubmit.do",
                data=login_data,
                headers=headers,
            )
            login_result = response.json()
            if login_result.get("toUrl"):
                # 获取session
                return self._get_session_by_redirect_url(login_result["toUrl"])
            return False
        except Exception as e:
            logger.error(f"密码登录失败: {e}")
            return False

    def _get_login_form(self) -> Optional[Dict[str, Any]]:
        """获取登录表单参数"""
        try:
            params = {
                "appId": APP_ID,
                "clientType": CLIENT_TYPE,
                "returnURL": RETURN_URL,
                "timeStamp": int(time.time() * 1000),
            }
            response = self.session.get(
                f"{WEB_URL}/api/portal/unifyLoginForPC.action", params=params
            )
            text = response.text
            if text:
                captcha_token = re.search(r"'captchaToken' value='(.+?)'", text).group(
                    1
                )
                lt = re.search(r'lt = "(.+?)"', text).group(1)
                param_id = re.search(r'paramId = "(.+?)"', text).group(1)
                req_id = re.search(r'reqId = "(.+?)"', text).group(1)
                return {
                    "captchaToken": captcha_token,
                    "lt": lt,
                    "paramId": param_id,
                    "reqId": req_id,
                }
            return None
        except:
            return None

    def _get_session_by_redirect_url(self, redirect_url: str) -> bool:
        """通过重定向URL获取session"""
        try:
            params = {
                "appId": APP_ID,
                "clientType": PC,
                "version": VERSION,
                "channelId": CID,
                "rand": int(time.time() * 1000),
                "redirectURL": redirect_url,
            }
            response = self.session.post(
                f"{API_URL}/getSessionForPC.action", params=params
            )
            data = response.json()
            if data.get("sessionKey"):
                self.session_key = data["sessionKey"]
                if data.get("accessToken"):
                    self.access_token = data["accessToken"]
                if data.get("refreshToken"):
                    self.refresh_token = data["refreshToken"]
                self._save_tokens()
                return True
            return False
        except Exception as e:
            logger.error(f"获取session失败: {e}")
            return False

    def _get_user_info(self) -> Dict[str, Any]:
        """获取磁盘空间信息"""
        try:
            url = f"{WEB_URL}/api/open/user/getUserInfoForPortal.action"
            response = self.session.get(url)
            data = response.json()
        except Exception as e:
            logger.error(f"获取空间信息失败: {e}")
            data = {}
        user_info = {
            "user_name": data.get("userExtResp", {}).get("nickName", ""),
            "user_face": data.get("userExtResp", {}).get("headPortraitUrl3", ""),
            "vip_level": "",
            "space_info": {
                "total": data.get("capacity", 0),
                "remain": data.get("available", 0),
                "use": (data.get("capacity", 0) - data.get("available", 0)),
            },
        }
        return user_info

    def init_info(self) -> bool:
        """初始化用户信息"""
        # 确保已登录
        if not self._ensure_login():
            return False
        self.user_info = self._get_user_info()
        # 保存用户信息到配置
        config = self.config_manager.get_storage(self.name)
        config["user_info"] = self.user_info
        self.config_manager.update_storage(self.name, config)
        return True

    def _get_fid_by_path(self, path: str) -> str:
        """获取文件夹ID"""
        # 检查缓存
        if fid := self.fid_cache.get(path):
            # logger.debug(f"使用缓存: {path}->{fid}")
            return fid
        # 分解路径
        parts = [p for p in path.split("/") if p]
        fid = ""
        pfid = "-11"
        current_path = "/"
        try:
            for part in parts:
                next_path = f"{current_path}/{part}".replace("//", "/")
                # 检查缓存
                if fid := self.fid_cache.get(next_path):
                    # 已有缓存跳下一层
                    current_path = next_path
                    pfid = fid
                    continue
                # 获取当前目录下的文件列表
                # logger.debug(f"读取路径: {next_path}，在pfid: {pfid}")
                files = self._get_all_files(pfid)
                # 查找目标文件
                for file_info in files:
                    file_path = f"{current_path}/{file_info['name']}".replace("//", "/")
                    self.fid_cache.set(file_path, file_info["id"], 60 * 10)
                    if file_path == next_path:
                        current_path = next_path
                        pfid = file_info["id"]
                    # logger.debug(f"  检查文件: {file_path}  {next_path}  {path}")
                    if file_path == path:
                        fid = file_info["id"]
            if fid:
                return fid
            raise FileNotFoundError(f"文件不存在: {current_path}")
        except Exception as e:
            logger.error(f"获取文件ID失败: {e}")
            raise

    def _get_all_files(self, folder_id: str) -> List[Dict[str, Any]]:
        """获取文件夹下的所有文件"""
        try:
            files = []
            page_num = 1
            while True:
                url = f"{WEB_URL}/api/open/file/listFiles.action"
                params = {
                    "pageSize": "60",
                    "pageNum": str(page_num),
                    "mediaType": "0",
                    "folderId": str(folder_id),
                    "iconOption": "5",
                    "orderBy": "lastOpTime",
                    "descending": "true",
                    # "sessionKey": self.session_key
                }
                response = self.session.get(url, params=params)
                # print(response.text)
                # {"errorCode":"InvalidSessionKey","errorMsg":"check ip error - curIp=240e:87c:ca2:3f4e:50d1:32fa:490e:a55a, cookiesIp=**************","success":null}
                data = response.json()
                if data.get("res_code") != 0:
                    break
                file_list = data.get("fileListAO", {})
                # 处理文件夹
                for f in file_list.get("folderList", []):
                    files.append(
                        {
                            "id": f["id"],
                            "name": f["name"],
                            "modified": self._convert_time(f["lastOpTime"]),
                            "created": self._convert_time(f["createDate"]),
                            "isdir": True,
                        }
                    )
                # 处理文件
                for f in file_list.get("fileList", []):
                    files.append(
                        {
                            "id": f["id"],
                            "name": f["name"],
                            "modified": self._convert_time(f["lastOpTime"]),
                            "created": self._convert_time(f["createDate"]),
                            "size": f.get("size", ""),
                            "isdir": False,
                            "type": f.get("mediaType", ""),
                        }
                    )
                if len(files) >= file_list.get("count", 0):
                    break
                page_num += 1
            return files
        except Exception as e:
            logger.error(f"获取文件列表失败: {e}")
            return []

    def list_files(self, path: str) -> Dict[str, Any]:
        """列出指定路径下的所有文件"""
        try:
            # 获取文件夹ID
            folder_id = self._get_fid_by_path(path)
            # 获取文件列表
            files = self._get_all_files(folder_id)
            # 格式化文件信息
            formatted_files = []
            for file_info in files:
                formatted_file = {
                    "name": file_info.get("name", ""),
                    "isdir": file_info.get("isdir", False),
                    "path": f"{path.rstrip('/')}/{file_info.get('name', '')}",
                    "size": file_info.get("size", ""),
                    "modified": file_info.get("modified", ""),
                    "created": file_info.get("created", ""),
                    # 可选参数
                    "id": file_info.get("id", ""),
                }
                formatted_files.append(formatted_file)
            return {"success": True, "data": formatted_files}
        except Exception as e:
            logger.error(f"列出文件失败: {e}")
            return {"success": False, "message": f"列出文件失败: {str(e)}"}

    def delete_file(self, path: str) -> bool:
        """删除指定文件"""
        try:
            # 分解路径
            parent_path = "/".join(path.split("/")[:-1]) or "/"
            file_name = path.split("/")[-1]
            # 获取父文件夹ID
            parent_id = self._get_fid_by_path(parent_path)
            # 获取文件列表，找到要删除的文件
            files = self._get_all_files(parent_id)
            target_file = None
            for file_info in files:
                if file_info.get("name") == file_name:
                    target_file = file_info
                    break
            if not target_file:
                logger.error(f"文件不存在: {path}")
                return False
            # 删除文件
            file_id = target_file.get("id")
            isdir = target_file.get("isdir", False)
            task_infos = [
                {
                    "fileId": file_id,
                    "fileName": file_name,
                    "isFolder": int(isdir),
                }
            ]
            data = {
                "type": "DELETE",
                "targetFolderId": "",
                "taskInfos": json.dumps(task_infos),
            }
            url = f"{WEB_URL}/api/open/batch/createBatchTask.action"
            params = {"sessionKey": self.session_key}
            response = self.session.post(url, data=data, params=params)
            result = response.json()
            if result.get("res_code") == 0:
                # 清除缓存
                if isdir:
                    self.fid_cache.delete(path)
                logger.info(f"删除文件成功: {path}")
                return True
            else:
                logger.error(f"删除文件失败: {result}")
                return False
        except Exception as e:
            logger.error(f"删除文件失败: {e}")
            return False

    def rename_file(self, path: str, new_name: str) -> bool:
        """重命名文件或目录"""
        try:
            # 获取文件信息
            file_info = self._get_file_info(path)
            if not file_info:
                logger.error(f"文件不存在: {path}")
                return False
            file_id = file_info.get("id")
            isdir = file_info.get("isdir", False)
            if isdir:
                # 重命名文件夹
                url = f"{WEB_URL}/api/open/file/renameFolder.action"
                data = {"folderId": file_id, "destFolderName": new_name}
            else:
                # 重命名文件
                url = f"{WEB_URL}/api/open/file/renameFile.action"
                data = {"fileId": file_id, "destFileName": new_name}
            # 发送重命名请求
            response = self.session.post(
                url,
                data=data,
                headers={"Content-Type": "application/x-www-form-urlencoded"},
            )
            result = response.json()
            if result.get("res_code") == 0:
                logger.info(f"重命名文件成功: {path} -> {new_name}")
                # 清除相关缓存
                self.fid_cache.delete(path)
                self.download_cache.delete(path)
                return True
            else:
                logger.error(f"重命名文件失败: {result.get('message', result)}")
                return False
        except Exception as e:
            logger.error(f"重命名文件失败: {e}")
            return False

    def get_download_url(self, path: str, file_info: Dict[str, Any] = {}) -> str:
        """获取下载 URL"""
        try:
            # 检查缓存
            cached_url = self.download_cache.get(path)
            if cached_url:
                return cached_url
            file_id = self._get_fid_by_path(path)
            if not file_id:
                raise ValueError("文件ID不存在")
            # 获取下载链接
            url = f"{WEB_URL}/api/open/file/getFileDownloadUrl.action"
            params = {
                "clientType": CLIENT_TYPE,
                "version": VERSION,
                "channelId": CID,
                "fileId": file_id,
            }
            response = self.session.get(url, params=params)
            data = response.json()
            if data.get("res_code") == 0:
                download_url = data.get("fileDownloadUrl", "")
                # 缓存下载链接（5分钟）
                self.download_cache.set(path, download_url, 300)
                return download_url
            else:
                raise Exception(f"获取下载链接失败: {data}")
        except Exception as e:
            logger.error(f"获取下载URL失败: {e}")
            return ""

    def _get_video_url(self, path: str, file_info: Dict[str, Any] = {}) -> str:
        """获取视频转码直链

        Args:
            path: 文件路径
            file_info: 文件信息

        Returns:
            str: 视频转码直链
        """
        try:
            file_id = self._get_fid_by_path(path)
            if not file_id:
                raise ValueError("文件ID不存在")
            url = f"{WEB_URL}/api/portal/getNewVlcVideoPlayUrl.action"
            params = {
                "fileId": file_id,
                "type": "2",
            }
            response = self.session.get(url, params=params)
            data = response.json()
            if data.get("res_code") == 0:
                return data["normal"]["url"]
        except Exception as e:
            logger.error(f"获取视频直链失败: {e}")
            return ""

    def get_strm_url(self, path: str, file_info: Dict[str, Any] = {}) -> str:
        """获取 strm URL"""
        return f"SMARTSTRM_BASE/smartstrm/{self.name}/{path}".replace("//", "/")

    def handle_strm_request(self, request: Request, file_path: str) -> Response:
        """处理 STRM 请求"""
        video_url = self._get_video_url(file_path)
        if not video_url:
            return Response("Video URL not found", 404)
        response = redirect(video_url, code=302)
        return response

    def get_file_data(self, path: str, file_info: Dict[str, Any] = {}) -> bytes:
        """获取文件二进制数据"""
        try:
            # 获取下载链接
            download_url = self.get_download_url(path, file_info)
            if not download_url:
                raise ValueError("无法获取下载链接")

            # 下载文件数据
            response = requests.get(download_url, timeout=30)
            response.raise_for_status()

            return response.content

        except Exception as e:
            logger.error(f"获取文件数据失败: {e}")
            return b""

    def _get_file_info(self, path: str) -> Optional[Dict[str, Any]]:
        """获取文件信息"""
        try:
            # 分解路径
            parent_path = "/".join(path.split("/")[:-1]) or "/"
            file_name = path.split("/")[-1]
            # 获取父文件夹ID
            parent_id = self._get_fid_by_path(parent_path)
            # 获取文件列表，找到目标文件
            files = self._get_all_files(parent_id)
            for file_info in files:
                if file_info.get("name") == file_name:
                    return file_info
            return None
        except Exception as e:
            logger.error(f"获取文件信息失败: {e}")
            return None
