import os
import time
import requests
import urllib.parse
from utils import utils
from typing import Dict, Any, List, Optional
from utils.kv_cache import KVCache
from ._base import BaseDriver
from core.log_manager import LogManager
from core.config_manager import ConfigManager

logger = LogManager.get_logger(__name__)


class Open115Driver(BaseDriver):
    """115开放平台驱动类"""

    DRIVER_TYPE = "open115"
    DRIVER_NAME = "115开放平台"

    DRIVER_CONFIG = {
        "access_token": {
            "type": "string",
            "required": True,
            "label": "访问令牌 (Access Token)",
            "tip": f"推荐使用<a class='text-decoration-none' href='/oauth/{DRIVER_TYPE}'>一键授权</a>，也可从 <a class='text-decoration-none' href='https://api.oplist.org' target='_blank'>OpenList Token 获取工具</a> 中手动获取",
        },
        "refresh_token": {
            "type": "string",
            "required": True,
            "label": "刷新令牌 (Refresh Token)",
            "tip": "",
        },
        "limit_rate": {
            "type": "number",
            "required": False,
            "label": "API 访问速率",
            "tip": "次/1s，限制每秒访问次数，留空则不限制，非VIP不限基本没法用",
        },
        "user_info": {
            "type": "hidden",
            "required": False,
            "label": "",
            "tip": "",
        },
    }

    DRIVER_TIPS = {
        "type": "info",
        "message": f"使用115开放平台API访问<br><b>注意：令牌组会滚动刷新，不可与它处共用。</b><br><a class='btn btn-primary mt-2 w-100' href='/oauth/{DRIVER_TYPE}'>一键授权</a>",
    }

    CLIENT_ID = "*********"
    BASE_URL = "https://proapi.115.com"
    AUTH_URL = "https://passportapi.115.com"
    USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"

    def __init__(self, config: Dict[str, Any]):
        """初始化115网盘驱动

        Args:
            access_token: 115开放平台访问令牌
            refresh_token: 115开放平台刷新令牌
        """
        self.name = None
        self.access_token = config["access_token"].strip()
        self.refresh_token = config["refresh_token"].strip()
        self.api_last_time = 0
        self.limit_rate = (
            config["limit_rate"]
            if isinstance(config.get("limit_rate"), (int, float))
            else 99999
        )
        self.api_interval = (1 / self.limit_rate) if self.limit_rate > 0 else 0
        # 缓存文件夹ID映射
        self.fid_cache = KVCache()
        self.fid_cache.set("/", "0", None)
        self.download_cache = KVCache()
        # 配置管理器（需要刷新token）
        self.config_manager = ConfigManager
        # 用户信息
        self.user_info = {}

    def init_info(self) -> bool:
        """初始化用户信息"""
        self.vip_url = self._get_vip_url()
        user_info = self._get_user_info()
        space_info = user_info.get("rt_space_info", {})
        self.user_info = {
            "user_name": user_info.get("user_name", ""),
            "user_face": user_info.get("user_face_m", ""),
            "vip_level": user_info.get("vip_info", {}).get("level_name", ""),
            "space_info": {
                "total": space_info.get("all_total", {}).get("size", 0),
                "remain": space_info.get("all_remain", {}).get("size", 0),
                "use": space_info.get("all_use", {}).get("size", 0),
            },
            "vip_url": self.vip_url or "",
        }
        config = self.config_manager.get_storage(self.name)
        config["user_info"] = self.user_info
        self.config_manager.update_storage(self.name, config)
        return True

    def _get_fid_by_path(self, path: str) -> Dict[str, Any]:
        """根据路径获取文件ID

        Args:
            path: 文件路径

        Returns:
            str: 文件ID
        """
        if not (fid := self.fid_cache.get(path)):
            payload = {"path": path}
            result = self._request(
                "POST",
                "/open/folder/get_info",
                data=payload,
            )
            if not result["success"]:
                return {"success": False, "message": result["message"]}
            data = result["data"]
            if not data["state"] or not data["data"]:
                return {"success": False, "message": "获取文件夹ID失败"}
            fid = data["data"]["file_id"]
            self.fid_cache.set(path, fid, 60)
        return {"success": True, "data": fid}

    def refresh_access_token(self) -> Dict[str, Any]:
        """刷新access_token

        Returns:
            Dict[str, Any]: 刷新结果
        """
        try:
            refresh_url = f"{self.AUTH_URL}/open/refreshToken"
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
            }
            data = {"refresh_token": self.refresh_token}
            response = requests.post(refresh_url, headers=headers, data=data)
            response.raise_for_status()
            result = response.json()
            if result.get("state"):
                self.access_token = result["data"].get("access_token")
                self.refresh_token = result["data"].get(
                    "refresh_token", self.refresh_token
                )
                self._save_new_token()
                logger.debug(
                    f"115 access_token 刷新成功: \naccess_token: {self.access_token}\nrefresh_token: {self.refresh_token}"
                )
                return {
                    "success": True,
                    "access_token": self.access_token,
                    "refresh_token": self.refresh_token,
                }
            else:
                code = result.get("code")
                error_msg = result.get("message", "未知错误")
                logger.error(f"刷新 access_token 失败: {code} {error_msg}")
                return {
                    "success": False,
                    "code": code,
                    "message": f"刷新 access_token 失败: {code} {error_msg}",
                }
        except Exception as e:
            logger.error(f"刷新 access_token 失败: {e}", exc_info=True)
            return {"success": False, "message": e}

    def _save_new_token(self) -> bool:
        try:
            full_config = self.config_manager.get_storage(self.name)
            if not full_config:
                return False
            full_config.update(
                {
                    "access_token": self.access_token,
                    "refresh_token": self.refresh_token,
                }
            )
            return self.config_manager.update_storage(self.name, full_config)
        except Exception as e:
            logger.error(f"保存access_token失败: {e}", exc_info=True)
            return False

    def _request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """发送HTTP请求

        Args:
            method: HTTP方法
            endpoint: API端点
            **kwargs: 其他请求参数

        Returns:
            Dict[str, Any]:
                success: 是否成功
                data: 响应数据
                message: 错误信息
        """
        # 主动限速和限制访问
        if self.limit_rate > 0:
            current_time = time.time()
            elapsed = current_time - self.api_last_time
            if self.api_interval and elapsed < self.api_interval:
                sleep = self.api_interval - elapsed
                logger.warning(f"API主动限频，等待 {round(sleep,2)}s")
                time.sleep(sleep)
            self.api_last_time = time.time()
        else:
            if self.limit_rate == 0:
                message = "访问频率限制为0，禁止访问API"
            elif self.limit_rate == -1:
                message = "刷新令牌失效，需重新授权"
            logger.error(message)
            return {"success": False, "message": message}

        try:
            url = f"{self.BASE_URL}{endpoint}"
            kwargs.setdefault("headers", {}).update(
                {
                    "Content-Type": "application/x-www-form-urlencoded",
                    "Authorization": f"Bearer {self.access_token}",
                }
            )
            # print(f"url: {url}")
            # print(f"kwargs: {kwargs}")
            response = requests.request(method, url, **kwargs)
            response.raise_for_status()
            data = response.json()
            # print(f"返回数据:\n{response.text}")
            # 检查是否是access_token过期的错误
            code = data.get("code", 0)
            if code == 406:  # 频率限制
                logger.warning(f"访问限制: {data['message']}")
                return {"success": False, "message": data["message"]}
            if code in [40140125, 40140126]:  # 过期, 校验失败
                logger.warning("检测到 access_token 过期，尝试刷新")
                refresh = self.refresh_access_token()
                if refresh["success"]:
                    # 重新请求
                    return self._request(method, url, **kwargs)
                else:
                    if (
                        refresh.get("code") >= 40140114
                        and refresh.get("code") <= 40140120
                    ):
                        self.limit_rate = -1
                    return {"success": False, "message": refresh["message"]}
            return {"success": True, "data": data}
        except Exception as e:
            logger.error(f"API请求失败: {e}", exc_info=True)
            return {"success": False, "message": e}

    def list_files(self, path: str) -> Dict[str, Any]:
        """列出指定路径下的所有文件

        Args:
            path: 要扫描的路径

        Returns:
            Dict[str, Any]:
                success: 是否成功
                data: List[Dict]: 文件信息列表，每个文件包含 name, isdir, path, size, modified, created 信息，其他信息可选
                message: 错误信息
        """
        try:

            list_merge = []
            offset = 0
            while True:
                result = self._get_fid_by_path(path)
                if not result["success"]:
                    return {"success": False, "message": result["message"]}
                fid = result["data"]
                result = self._request(
                    "GET",
                    "/open/ufile/files",
                    params={
                        "cid": fid,
                        "offset": offset,
                        "limit": 1150,
                        "asc": 1,
                        "o": "file_name",
                        "show_dir": True,
                    },
                )
                if not result["success"]:
                    return {"success": False, "message": result["message"]}
                if result["data"].get("data"):
                    list_merge += result["data"]["data"]
                    offset += 1150
                if result["data"].get("count", 0) <= offset:
                    break

            files = []
            for f in list_merge:
                f_path = os.path.join(path, f["fn"]).replace("\\", "/")
                if f.get("fid"):
                    self.fid_cache.set(f_path, f["fid"], 60)
                files.append(
                    {
                        "name": f["fn"],
                        "isdir": f["fc"] == "0",
                        "path": f_path,
                        "size": f["fs"] if f["fc"] == "1" else "",
                        "modified": f["upt"],
                        "created": f["uppt"],
                        "type": f.get("type", "dir"),
                        "fid": f.get("fid"),
                        "pick_code": f.get("pick_code"),
                    }
                )
            return {"success": True, "data": files}
        except Exception as e:
            logger.error(f"列出文件失败: {e}", exc_info=True)
            return {"success": False, "message": e}

    def delete_file(self, path: str) -> bool:
        """删除指定文件

        Args:
            path: 文件路径

        Returns:
            bool: 是否删除成功
        """
        try:
            # 获取文件信息
            result = self._get_fid_by_path(path)
            if not result["success"]:
                return False
            file_id = result["data"]
            # 删除文件
            result = self._request(
                "POST", "/open/ufile/delete", data={"file_ids": file_id}
            )
            if result["success"]:
                data = result["data"]
                return data["state"]
        except Exception as e:
            logger.error(f"删除文件失败: {e}")
            return False

    def rename_file(self, path: str, new_name: str) -> bool:
        """重命名文件或目录

        Args:
            path: 原文件路径
            new_name: 新文件名

        Returns:
            bool: 是否重命名成功
        """
        try:
            # 获取文件信息
            result = self._get_fid_by_path(path)
            if not result["success"]:
                return False
            file_id = result["data"]
            # 重命名文件
            result = self._request(
                "POST",
                "/open/ufile/update",
                data={"file_id": file_id, "file_name": new_name},
            )
            if result["success"]:
                data = result["data"]
                return data["state"]
        except Exception as e:
            logger.error(f"重命名文件失败: {e}")
            return False

    def get_download_url(self, path: str, file_info: Dict[str, Any] = {}) -> str:
        """获取下载 URL

        Args:
            path: 文件路径
            file_info: 文件信息

        Returns:
            str: 下载 URL
        """
        try:
            payload = {"path": path}
            result = self._request(
                "POST",
                "/open/folder/get_info",
                data=payload,
            )
            if not result["success"]:
                return ""
            data = result["data"]
            file_id = data["data"].get("file_id")
            pick_code = data["data"].get("pick_code")

            headers = {
                "User-Agent": file_info.get("download_ua", self.USER_AGENT),
            }

            # 读取缓存
            file_flag = utils.md5(f"{pick_code}{headers['User-Agent']}")
            if download_url := self.download_cache.get(file_flag):
                logger.debug(f"使用缓存直链: {pick_code}")
                return download_url

            result = self._request(
                "POST",
                "/open/ufile/downurl",
                headers=headers,
                data={"pick_code": pick_code},
            )
            if not result["success"]:
                return ""
            data = result["data"]
            if data["state"]:
                download_url = data["data"][file_id]["url"]["url"]
                self.download_cache.set(file_flag, download_url, 600)
                return download_url
            return ""
        except Exception as e:
            logger.error(f"获取下载URL失败: {e}", exc_info=True)
            return ""

    def get_strm_url(self, path: str, file_info: Dict[str, Any] = {}) -> str:
        """获取 strm URL

        Args:
            path: 文件路径
            file_info: 文件信息
        """
        # return self.get_download_url(path, file_info)
        return f"SMARTSTRM_BASE/smartstrm/{self.name}/{path}".replace("//", "/")

    def get_file_data(self, path: str, file_info: Dict[str, Any] = {}) -> bytes:
        """获取文件二进制数据

        Args:
            path: 文件路径
            file_info: 文件信息
        Returns:
            bytes: 文件二进制数据
        """
        try:
            download_url = self.get_download_url(path, file_info)
            if not download_url:
                logger.error(f"无法获取下载URL: {path}")
                return b""
            headers = {
                "User-Agent": file_info.get("download_ua", self.USER_AGENT),
            }
            response = requests.get(download_url, headers=headers, timeout=30)
            response.raise_for_status()
            return response.content
        except Exception as e:
            logger.error(f"获取文件数据失败: {e}")
            return b""

    def _get_vip_url(self) -> str:
        """
        获取115网盘VIP推广二维码URL。

        Returns:
            str: 用于推广VIP的二维码URL，如果获取失败则返回空字符串。
        """
        result = self._request(
            "GET",
            "/open/vip/qr_url?open_device=smartstrm",
        )
        if not result["success"]:
            return ""
        data = result["data"]
        return data["data"]["qrcode_url"]

    def _get_user_info(self) -> Dict[str, Any]:
        """获取用户信息
        https://www.yuque.com/115yun/open/ot1litggzxa1czww
        Returns:
            Dict[str, Any]:
                user_id: 用户ID
                user_name: 用户名
                user_face_s, user_face_m, user_face_l: 用户头像，小中大
                vip_info: 会员信息
                    level_name: 会员等级名称
                    expire: 会员过期时间戳
                rt_space_info: 空间信息
                    all_total: 总空间
                        size: 已使用空间
                        size_format: 剩余空间
                    all_remain: 剩余空间
                    all_use: 已使用空间
        """
        result = self._request(
            "GET",
            "/open/user/info",
        )
        if not result.get("success"):
            return {}
        data = result["data"]
        return data["data"]

    @classmethod
    def get_oauth_url(cls, redirect_uri: str, state: str = None) -> str:
        """
        生成授权码模式的授权 URL。
        用户需要访问此 URL 进行授权。

        Args:
            redirect_uri (str): 授权成功后重定向的地址，需要在 115 开放平台设置。
            state (str, optional): 推荐使用随机值，用于防止 CSRF 攻击。

        Returns:
            str: 完整的授权 URL。
        """
        params = {
            "client_id": cls.CLIENT_ID,
            "redirect_uri": redirect_uri,
            "response_type": "code",
        }
        if state:
            params["state"] = state
        query_string = urllib.parse.urlencode(params)
        return f"{cls.AUTH_URL}/open/authorize?{query_string}"
