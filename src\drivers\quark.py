import os
import re
import requests
import urllib.parse
from typing import Dict, Any, List, Optional
from ._base import BaseDriver
from utils.kv_cache import KVCache
from core.log_manager import LogManager
from core.config_manager import ConfigManager
from core.license_manager import LicenseManager
from flask import Request, Response, redirect, stream_with_context

logger = LogManager.get_logger(__name__)


class QuarkDriver(BaseDriver):
    """夸克网盘驱动类"""

    DRIVER_TYPE = "quark"
    DRIVER_NAME = "夸克网盘"

    DRIVER_CONFIG = {
        "cookie": {
            "type": "string",
            "required": True,
            "label": "Cookie",
            "tip": "从浏览器 F12 获取的 Cookie",
        },
        "strm_mode": {
            "type": "options",
            "options": {
                "proxy": "原码代理",
                "fallback": "智能回落 (Pro)",
                "direct": "仅转码直链",
            },
            "required": True,
            "default": "direct",
            "label": "STRM 模式",
            "tip": " - 原码代理：文件资源请求通过 SmartStrm 代理，受限于服务器带宽<br>- 智能回落：优先使用转码直链，不兼容的格式回落到走代理<br>- 仅转码直链：仅使用转码直链，客户端不兼容则无法播放<br>* 留空默认仅转码直链模式",
        },
    }

    DRIVER_TIPS = {
        "type": "info",
        "message": "使用夸克网盘 PC API 访问<br>由 SmartStrm 提供 STRM 解析，支持直链和智能回落。",
    }

    BASE_URL = "https://drive-pc.quark.cn"
    USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) quark-cloud-drive/3.19.0 Chrome/112.0.5615.165 Electron/******** Safari/537.36 Channel/pckk_other_ch"
    M3U8_WHITELIST = ["afusekt", "vidhub", "Emby/3.2."]

    def __init__(self, config: Dict[str, Any]):
        """初始化夸克网盘驱动

        Args:
            cookie: 夸克网盘的 Cookie
        """
        self.name = None
        self.cookie = config["cookie"].strip()
        self.strm_mode = config.get("strm_mode", "direct")
        self.fid_cache = KVCache()
        self.fid_cache.set("/", "0", None)
        self.download_cache = KVCache()
        self.playback_cache = KVCache()
        self.headers = {
            "cookie": self.cookie,
            "content-type": "application/json",
            "user-agent": self.USER_AGENT,
        }
        self.config_manager = ConfigManager
        self.license_manager = LicenseManager

    def init_info(self) -> bool:
        """初始化用户信息"""
        user_info = self._get_user_info()
        config = self.config_manager.get_storage(self.name)
        config["user_info"] = user_info
        self.config_manager.update_storage(self.name, config)
        return True

    def _send_request(
        self, method: str, url: str, **kwargs
    ) -> Optional[requests.Response]:
        """发送请求到夸克网盘 API

        Args:
            method: 请求方法
            url: 请求地址
            **kwargs: 请求参数

        Returns:
            Optional[requests.Response]: 响应对象，如果请求失败则返回 None
        """
        try:
            if "headers" in kwargs:
                headers = kwargs["headers"]
                del kwargs["headers"]
            else:
                headers = self.headers
            response = requests.request(method, url, headers=headers, **kwargs)
            return response
        except Exception as e:
            logger.error(f"请求夸克网盘 API 失败: {e}")
            return None

    def _get_fid_by_path(self, path: str) -> str:
        """获取指定路径的文件夹 ID

        Args:
            path: 文件夹路径

        Returns:
            str: 文件夹 ID
        """
        if not (fid := self.fid_cache.get(path)):
            url = f"{self.BASE_URL}/1/clouddrive/file/info/path_list"
            querystring = {"pr": "ucpro", "fr": "pc"}
            payload = {"file_path": [path], "namespace": "0"}
            response = self._send_request("POST", url, json=payload, params=querystring)
            if response and response.status_code == 200:
                data = response.json()
                if data["code"] == 0 and data["data"]:
                    fid = data["data"][0]["fid"]
                    self.fid_cache.set(path, fid, 60)
        return fid

    def _get_fid_by_fdir(self, path: str, file_info: Dict[str, Any] = {}) -> str:
        """根据路径获取文件的 fid 并触发缓存整个父目录的 fid

        Args:
            path: 文件路径
            file_info: 文件信息

        Returns:
            str: 文件的 fid
        """
        if not (fid := self.fid_cache.get(path)):
            file_name = os.path.basename(path)
            result = self.list_files(os.path.dirname(path))
            if not result["success"]:
                return ""
            files = result["data"]
            file_info = next((f for f in files if f["name"] == file_name), None)
            if not file_info:
                return ""
            fid = file_info["fid"]
        return fid

    def list_files(self, path: str = "/") -> Dict[str, Any]:
        """列出指定路径下的文件

        Args:
            path: 要列出的路径

        Returns:
            Dict[str, Any]:
                success: 是否成功
                data: List[Dict]: 文件信息列表，每个文件包含 name, isdir, path, size, modified, created 信息，其他信息可选
                message: 错误信息
        """
        try:
            pdir_fid = self._get_fid_by_path(path)
            if not pdir_fid:
                logger.error(f"获取文件夹 ID 失败: {path}")
                return {"success": False, "message": "获取文件夹 ID 失败"}

            list_merge = []
            page = 1
            while True:
                url = f"{self.BASE_URL}/1/clouddrive/file/sort"
                querystring = {
                    "pr": "ucpro",
                    "fr": "pc",
                    "uc_param_str": "",
                    "pdir_fid": pdir_fid,
                    "_page": page,
                    "_size": 200,
                    "_fetch_total": 1,
                    "_fetch_sub_dirs": 0,
                    "_sort": "file_type:asc,updated_at:desc",
                    "_fetch_full_path": 1,
                }
                response = self._send_request("GET", url, params=querystring)
                if not response or response.status_code != 200:
                    return {"success": False, "message": "API请求失败"}

                data = response.json()
                if data["code"] != 0:
                    return {"success": False, "message": data["message"]}
                if data["data"]["list"]:
                    list_merge += data["data"]["list"]
                    page += 1
                else:
                    break
                if len(list_merge) >= data["metadata"]["_total"]:
                    break

            files = []
            for f in list_merge:
                # 缓存 fid ，减少后续请求
                f_path = os.path.join(path, f["file_name"]).replace("\\", "/")
                self.fid_cache.set(f_path, f["fid"], 60)
                files.append(
                    {
                        "name": f["file_name"],
                        "isdir": f["dir"],
                        "path": f_path,
                        "size": f["size"] if not f["dir"] else "",
                        "modified": f["updated_at"] / 1000,
                        "created": f["created_at"] / 1000,
                        # 可选参数
                        "type": f["file_type"],
                        "fid": f["fid"],
                    }
                )
            return {"success": True, "data": files}
        except Exception as e:
            logger.error(f"列出文件失败: {e}", exc_info=True)
            return {"success": False, "message": e}

    def delete_file(self, path: str) -> bool:
        """删除指定文件

        Args:
            path: 文件路径

        Returns:
            bool: 是否删除成功
        """
        try:
            fid = self._get_fid_by_fdir(path)
            if not fid:
                return False

            url = f"{self.BASE_URL}/1/clouddrive/file/delete"
            querystring = {"pr": "ucpro", "fr": "pc", "uc_param_str": ""}
            payload = {
                "action_type": 2,
                "filelist": [fid],
                "exclude_fids": [],
            }
            response = self._send_request("POST", url, json=payload, params=querystring)
            if not response or response.status_code != 200:
                return False

            data = response.json()
            return data["code"] == 0
        except Exception as e:
            logger.error(f"删除文件失败: {e}")
            return False

    def rename_file(self, path: str, new_name: str) -> bool:
        """重命名文件或目录

        Args:
            path: 原文件路径
            new_name: 新文件名

        Returns:
            bool: 是否重命名成功
        """
        try:
            fid = self._get_fid_by_fdir(path)
            if not fid:
                return False

            url = f"{self.BASE_URL}/1/clouddrive/file/rename"
            querystring = {"pr": "ucpro", "fr": "pc", "uc_param_str": ""}
            payload = {"fid": fid, "file_name": new_name}
            response = self._send_request("POST", url, json=payload, params=querystring)
            if not response or response.status_code != 200:
                return False

            data = response.json()
            return data["code"] == 0
        except Exception as e:
            logger.error(f"重命名文件失败: {e}")
            return False

    def get_download_url(self, path: str, file_info: Dict[str, Any] = {}) -> str:
        """获取下载 URL

        Args:
            path: 文件路径
            file_info: 文件信息

        Returns:
            str: 下载 URL
        """
        return ""

    def _get_video_url(self, path: str, file_info: Dict[str, Any] = {}) -> str:
        """获取视频转码直链

        Args:
            path: 文件路径
            file_info: 文件信息

        Returns:
            str: 视频转码直链
        """
        # 判断是否为视频文件
        video_extensions = [".mp4", ".mkv", ".avi", ".mov", ".3gp", ".flv"]
        is_video = any(path.lower().endswith(ext) for ext in video_extensions)
        if is_video:
            # 使用视频播放API获取直链
            try:
                fid = file_info.get("fid") or self._get_fid_by_fdir(path)
                if not fid:
                    return ""

                # 读取缓存
                if play_url := self.playback_cache.get(fid):
                    logger.debug(f"使用缓存转码直链: {fid}")
                    return play_url

                url = f"{self.BASE_URL}/1/clouddrive/file/v2/play/project"
                querystring = {"pr": "ucpro", "fr": "pc", "uc_param_str": ""}
                payload = {
                    "fid": fid,
                    "resolutions": "low,normal,high,super,2k,4k",
                    "supports": "fmp4_av,m3u8,dolby_vision",
                }
                response = self._send_request(
                    "POST", url, json=payload, params=querystring
                )
                if response.status_code == 200:
                    data = response.json()
                    if data["code"] == 0 and data["data"]:
                        play_url = data["data"]["video_list"][0]["video_info"]["url"]
                        # 缓存播放信息
                        self.playback_cache.set(fid, play_url, 600)
                        return play_url
            except Exception as e:
                logger.error(f"获取视频转码直链失败: {e}")
        return ""

    def get_strm_url(self, path: str, file_info: Dict[str, Any] = {}) -> str:
        """获取 STRM URL

        Args:
            path: 文件路径
            file_info: 文件信息
        """
        return f"SMARTSTRM_BASE/smartstrm/{self.name}/{path}".replace("//", "/")

    def _init_download_data(
        self, path: str = "", file_info: Dict[str, Any] = {}
    ) -> dict:
        """获取下载信息

        Args:
            path: 文件路径
            file_info: 文件信息

        Returns:
            dict: 下载信息
                download_url: 下载 URL
                size: 文件大小
                format_type: 文件格式
                file_name: 文件名
                cookie: Cookie
        """
        try:
            fid = file_info.get("fid") or self._get_fid_by_fdir(path)
            if not fid:
                return {}

            # 读取缓存
            if data := self.download_cache.get(fid):
                logger.debug(f"使用缓存下载信息: {path}")
                return data

            url = f"{self.BASE_URL}/1/clouddrive/file/download"
            querystring = {"pr": "ucpro", "fr": "pc", "uc_param_str": ""}
            payload = {"fids": [fid], "speedup_session": ""}
            response = self._send_request("POST", url, json=payload, params=querystring)
            if not response or response.status_code != 200:
                logger.error(f"获取下载 URL 失败: {response.text}")
                return {}

            data = response.json()
            if data["code"] == 0 and data["data"]:
                set_cookie = response.cookies.get_dict()
                cookie_str = "; ".join(
                    [f"{key}={value}" for key, value in set_cookie.items()]
                )
                data = data["data"][0]
                data["cookie"] = cookie_str
                # 缓存下载信息
                self.download_cache.set(fid, data, 600)
                return data
            return {}
        except Exception as e:
            logger.error(f"获取下载信息失败: {e}")
            return {}

    def get_file_data(self, path: str, file_info: Dict[str, Any] = {}) -> bytes:
        """获取文件二进制数据

        Args:
            path: 文件路径
            file_info: 文件信息
        Returns:
            bytes: 文件二进制数据
        """
        try:
            download_info = self._init_download_data(path, file_info)
            if not download_info:
                logger.error(f"获取下载信息失败: {path}")
                return b""

            response = requests.get(
                download_info["download_url"],
                headers={
                    "User-Agent": self.USER_AGENT,
                    "Cookie": download_info["cookie"],
                },
            )
            response.raise_for_status()
            return response.content
        except Exception as e:
            logger.error(f"获取文件数据失败: {e}")
            return b""

    def _get_user_info(self) -> Dict[str, Any]:
        """获取用户信息"""
        VIP_MAP = {
            "NORMAL": "普通用户",
            "VIP": "VIP",
            "EXP_SVIP": "88VIP",
            "SUPER_VIP": "SVIP",
            "Z_VIP": "SVIP+",
            # "MINI_VIP": "MINI_VIP",
            # "EXP_VIP": "EXP_VIP",
        }
        NO_FACE = "https://image.quark.cn/s/uae/g/3o/broccoli/resource/202306/0d16b010-0f13-11ee-a22d-496578f265e7.png"
        # 用户名、头像
        response = self._send_request(
            "GET", "https://pan.quark.cn/account/info?fr=pc&platform=pc"
        )
        info1 = response.json().get("data", {}) if response else {}
        # 等级、空间
        response = self._send_request(
            "GET",
            f"{self.BASE_URL}/1/clouddrive/member?pr=ucpro&fr=pc",
        )
        info2 = response.json().get("data", {}) if response else {}
        member_type = info2.get("member_type", "")
        # 合并用户信息
        user_info = {
            "user_name": info1.get("nickname", ""),
            "user_face": info1.get("avatarUri") or NO_FACE,
            "vip_level": VIP_MAP.get(member_type, member_type),
            "space_info": {
                "total": info2.get("total_capacity", 0),
                "remain": info2.get("total_capacity", 0) - info2.get("use_capacity", 0),
                "use": info2.get("use_capacity", 0),
            },
        }
        return user_info

    def handle_strm_request(self, request: Request, file_path: str) -> Response:
        """处理 STRM 请求"""
        file_name = os.path.basename(file_path)
        strm_mode = self.strm_mode
        # 回落模式许可
        if not self.license_manager.is_licensed("quark_fallback"):
            if self.strm_mode == "fallback":
                logger.warning("夸克智能回落模式未获许可，已切换到仅转码直链模式")
                self.strm_mode = "direct"
        if self.strm_mode == "proxy":
            # 全代理
            response = self.handle_download_request(request, file_path)
        if self.strm_mode == "direct":
            # 仅转码直链
            video_url = self._get_video_url(file_path)
            response = redirect(video_url, code=302)
        if self.strm_mode == "fallback":
            # 智能回落
            video_url = self._get_video_url(file_path)
            # 获取 UA，判断兼容的客户端
            req_ua = request.user_agent.string
            logger.debug(f"🟡 UA: {req_ua}")
            if ".m3u8" not in video_url or any(
                ua.lower() in req_ua.lower() for ua in self.M3U8_WHITELIST
            ):
                strm_mode += "->direct"
                response = redirect(video_url, code=302)
            else:
                strm_mode += "->proxy"
                response = self.handle_download_request(request, file_path)
        logger.info(f"STRM {strm_mode} : [{self.name}]{file_name}")
        return response

    def handle_download_request(self, request: Request, file_path: str) -> Response:
        """处理下载请求"""
        d_info = self._init_download_data(file_path)
        if not d_info:
            return Response("Failed to get download data", status=500)

        url = d_info["download_url"]
        file_size = d_info["size"]
        start, end = 0, file_size

        # 默认响应头
        status_code = 200
        file_name_encoded = urllib.parse.quote(d_info["file_name"])
        resp_headers = {
            "Content-Length": str(file_size),
            "Content-Type": d_info["format_type"],
            "Content-Disposition": f"attachment; filename=\"{file_name_encoded}\"; filename*=utf-8''{file_name_encoded}",
            "Accept-Ranges": "bytes",
            "Access-Control-Allow-Origin": "*",
        }

        # 获取客户端的 Range 请求头
        if range_header := request.headers.get("Range"):
            match = re.match(r"bytes=(\d+)-(\d*)", range_header)
            if match:
                range_start = int(match.group(1))
                range_end_str = match.group(2)  # 可能是空字符串或具体数字
                range_end = int(range_end_str) if range_end_str else file_size - 1
                # 验证请求范围是否有效
                if 0 <= range_start < file_size and range_start <= range_end:
                    start = range_start
                    # +1 是因为HTTP Range是包容的，Python切片是排他的
                    target_end = min(range_end + 1, file_size)
                    content_length = target_end - start
                    resp_headers["Content-Range"] = (
                        f"bytes {start}-{target_end-1}/{file_size}"
                    )
                    resp_headers["Content-Length"] = str(content_length)
                    status_code = 206  # Partial Content
                else:
                    # 无效的Range请求，返回416
                    resp_headers["Content-Range"] = f"bytes */{file_size}"
                    return Response(status=416, headers=resp_headers)

        @stream_with_context
        def generate_chunks():
            """
            此生成器函数将每次从远程URL请求的10MB数据块yield给Flask响应。
            """
            current_start = start
            target_end = end
            # 分片大小
            chunk_size = 10 * 1024 * 1024
            while current_start < target_end:
                _end = min(current_start + chunk_size, target_end)
                _range = f"bytes={current_start}-{_end-1}"
                logger.debug(f"Requesting range: {_range}")
                current_start = _end
                try:
                    req_headers = {
                        "Range": _range,
                        "User-Agent": self.USER_AGENT,
                        "Cookie": d_info["cookie"],
                    }
                    resp = requests.get(
                        url,
                        headers=req_headers,
                        stream=True,
                        allow_redirects=True,
                        timeout=(10, 30),
                    )
                    # 检查响应状态码
                    if resp.status_code != 206:
                        raise IOError(f"Unexpected status code: {resp.status_code}")
                    # 将远程响应的内容分块 yield 出去
                    for chunk in resp.iter_content(chunk_size=8192):
                        if chunk:
                            yield chunk
                except requests.exceptions.RequestException as e:
                    raise IOError(f"Network error during download: {e}")
                except Exception as e:
                    logger.error(f"Error during chunk processing: {e}")
                    raise

        # 创建流式响应
        response = Response(
            generate_chunks(),
            status=status_code,
            headers=resp_headers,
        )
        return response
