<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - SmartStrm</title>
    <link href="/static/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/css/bootstrap-icons.min.css" rel="stylesheet">
    <style>
        body {
            height: 100vh;
            display: flex;
            align-items: center;
            background-color: #f8f9fa;
        }

        .login-container {
            max-width: 400px;
            width: 100%;
            padding: 15px;
            margin: auto;
        }

        .login-card {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .login-header {
            text-align: center;
            padding: 2rem 1rem;
            border-bottom: 1px solid #dee2e6;
        }

        .login-body {
            padding: 2rem;
        }

        .form-floating {
            margin-bottom: 1rem;
        }

        .btn-login {
            width: 100%;
            padding: 0.75rem;
        }
    </style>
</head>

<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <p><img src="/static/img/icon.svg" style="width: 48px; height: 48px;"></p>
                <h1 class="h3 mb-0">SmartStrm</h1>
                <p class="text-muted mt-2 mb-0">媒体文件管理工具</p>
            </div>
            <div class="login-body">
                <form id="loginForm" method="post" action="/login">
                    {% if error %}
                    <div class="alert alert-danger mt-0 mb-3" role="alert">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        [[ error ]]
                    </div>
                    {% endif %}
                    <div class="form-floating">
                        <input type="text" class="form-control" id="username" name="username" placeholder="用户名" required>
                        <label for="username">用户名</label>
                    </div>
                    <div class="form-floating">
                        <input type="password" class="form-control" id="password" name="password" placeholder="密码" required>
                        <label for="password">密码</label>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-login">
                            登录
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</body>

</html>