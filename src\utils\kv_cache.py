import time
from typing import Any, Optional, Dict, Tuple, Union  # 引入类型提示


class KVCache:
    """
    一个带过期时间的 KV 缓存类。

    - 支持为每个键设置独立的过期时间 (TTL)。
    - 惰性清理：仅在尝试获取或设置一个键时，才检查并清除其过期状态。
    - 主动清理：提供 `clean_expired` 方法，可手动或在特定场景下触发全面清理。
    - 使用 time.monotonic() 确保过期时间计算不受系统时钟调整影响。
    """

    def __init__(self, default_ttl: Optional[Union[int, float]] = None):
        """
        初始化 KVCache。

        :param default_ttl: 默认的过期时间（秒）。如果为 None，则项永不过期。
                            在 set() 方法中如果不指定 ttl，将使用此默认值。
        """
        # 内部存储：{key: (value, expiry_time)}
        # expiry_time 为 None 表示永不过期。
        # 使用元组存储可轻微优化内存，并避免字典嵌套查询 'value' 和 'expiry_time'。
        self._cache: Dict[Any, Tuple[Any, Optional[Union[int, float]]]] = {}
        self._default_ttl = default_ttl

    def set(self, key: Any, value: Any, ttl: Optional[Union[int, float]] = None) -> None:
        """
        设置缓存项。

        :param key: 键。
        :param value: 值。
        :param ttl: 可选的过期时间（秒）。如果为 None，则使用 default_ttl。
                    如果 default_ttl 也为 None，则该项永不过期。
        """
        effective_ttl: Optional[Union[int, float]] = ttl if ttl is not None else self._default_ttl

        expiry_time: Optional[Union[int, float]] = None
        if effective_ttl is not None:
            expiry_time = time.monotonic() + effective_ttl

        self._cache[key] = (value, expiry_time)

    def get(self, key: Any) -> Optional[Any]:
        """
        获取缓存值。

        如果键不存在或已过期，则返回 None。
        :param key: 键。
        :return: 值，或 None。
        """
        data = self._cache.get(key)
        if data is None:
            return None

        value, expiry_time = data
        current_time = time.monotonic()

        if expiry_time is not None and current_time > expiry_time:
            del self._cache[key]  # 惰性清理过期项
            return None

        return value

    def delete(self, key: Any) -> bool:
        """
        删除缓存项。

        :param key: 键。
        :return: 如果成功删除返回 True，否则返回 False。
        """
        if key in self._cache:
            del self._cache[key]
            return True
        return False

    def clear(self) -> None:
        """清空所有缓存项。"""
        self._cache.clear()

    def __len__(self) -> int:
        """
        返回缓存中当前有效（未过期）的项数。

        在计算长度时，会主动清理一次，确保返回的是有效项数量。
        注意：此操作可能会触发对所有项的过期检查和删除，对于超大型缓存可能稍慢。
        """
        self.clean_expired()
        return len(self._cache)

    def __contains__(self, key: Any) -> bool:
        """
        检查键是否存在且未过期。
        """
        return self.get(key) is not None

    def clean_expired(self) -> None:
        """
        主动遍历并清理所有已过期项。

        此方法在需要精确了解缓存大小或减少内存占用时可能有用。
        注意：对于大型缓存，此操作可能较慢。
        """
        keys_to_delete = []
        current_time = time.monotonic()
        # 优化：在迭代器上安全删除
        for key, (value, expiry_time) in self._cache.items():
            if expiry_time is not None and current_time > expiry_time:
                keys_to_delete.append(key)

        for key in keys_to_delete:
            del self._cache[key]


# --- 使用示例 (与原代码保持一致，验证功能) ---
if __name__ == "__main__":
    print("--- 示例 1: 基本功能和默认过期时间 ---")
    cache = KVCache(default_ttl=2)  # 默认 2 秒过期
    cache.set("name", "Alice")
    cache.set("age", 30, ttl=1)  # age 设置为 1 秒过期

    print(f"Name: {cache.get('name')}")
    print(f"Age: {cache.get('age')}")

    time.sleep(1.2)

    print(f"\nAfter 1.2 seconds:")
    print(f"Name: {cache.get('name')}")
    print(f"Age: {cache.get('age')}")

    time.sleep(1)

    print(f"\nAfter another 1 second:")
    print(f"Name: {cache.get('name')}")
    print(f"Age: {cache.get('age')}")

    print("\n--- 示例 2: 无默认过期时间，手动设置 ---")
    cache_no_default = KVCache()
    cache_no_default.set("item1", "value1")
    cache_no_default.set("item2", "value2", ttl=0.5)

    print(f"Item1: {cache_no_default.get('item1')}")
    print(f"Item2: {cache_no_default.get('item2')}")

    time.sleep(0.6)

    print(f"\nAfter 0.6 seconds:")
    print(f"Item1: {cache_no_default.get('item1')}")
    print(f"Item2: {cache_no_default.get('item2')}")

    print("\n--- 示例 3: 检查长度和清理 ---")
    cache_len = KVCache(default_ttl=1)
    cache_len.set("k1", "v1")
    cache_len.set("k2", "v2", ttl=0.5)
    cache_len.set("k3", "v3")

    print(f"Initial internal cache length: {len(cache_len._cache)}")
    print(f"Valid cache length (before sleep): {len(cache_len)}")

    time.sleep(0.6)
    print(f"k2 after sleep: {cache_len.get('k2')}")
    print(f"Valid cache length (after k2 access): {len(cache_len)}")

    time.sleep(0.5)
    print(
        f"Valid cache length (after more sleep, before proactive clean): {len(cache_len)}"
    )
    cache_len.clean_expired()
    print(f"Valid cache length (after proactive clean): {len(cache_len)}")

    print("\n--- 示例 4: 使用 'in' 操作符 ---")
    cache_in = KVCache(default_ttl=1)
    cache_in.set("test_key", "test_value")
    print(f"'test_key' in cache? {('test_key' in cache_in)}")
    time.sleep(1.1)
    print(f"'test_key' in cache after expiry? {('test_key' in cache_in)}")
