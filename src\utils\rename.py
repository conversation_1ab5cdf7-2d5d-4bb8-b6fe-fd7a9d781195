import re
from typing import List, Dict, Any


class MagicRename:

    magic_variable = {
        "{TITLE}": [
            r"^[\u4e00-\u9fa5]{2,}(?=\.)",
            r"^[\w\.]{2,}(?=[._]S\d)",
        ],
        "{I}": 1,
        "{EXT}": [r"(?<=\.)\w+$"],
        "{CHINESE}": [r"[\u4e00-\u9fa5]{2,}"],
        "{DATE}": [
            r"(18|19|20)?\d{2}[\.\-/年]\d{1,2}[\.\-/月]\d{1,2}",
            r"(?<!\d)[12]\d{3}[01]?\d[0123]?\d",
            r"(?<!\d)[01]?\d[\.\-/月][0123]?\d",
        ],
        "{YEAR}": [r"(?<!\d)(18|19|20)\d{2}(?!\d)"],
        "{S}": [r"(?<=[Ss])\d{1,2}(?=[EeXx])", r"(?<=[Ss])\d{1,2}"],
        "{SXX}": [r"[Ss]\d{1,2}(?=[EeXx])", r"[Ss]\d{1,2}"],
        "{E}": [
            r"(?<=[Ss]\d\d[Ee])\d{1,3}",
            r"(?<=[Ee])\d{1,3}",
            r"(?<=[Ee][Pp])\d{1,3}",
            r"(?<=第)\d{1,3}(?=[集期话部篇])",
            r"(?<!\d)\d{1,3}(?=[集期话部篇])",
            r"(?!.*19)(?!.*20)(?<=[\._])\d{1,3}(?=[\._])",
            r"^\d{1,3}(?=\.\w+)",
            r"(?<!\d)\d{1,3}(?!\d)(?!$)",
        ],
        "{PART}": [
            r"(?<=[集期话部篇第])[上中下一二三四五六七八九十]",
            r"[上中下一二三四五六七八九十]",
        ],
        "{VER}": [r"[\u4e00-\u9fa5]+版"],
        "{QUALITY}": [r"\d{4}[Pp]"],
    }

    rename_ext = [
        "mp4",
        "mkv",
        "avi",
        "mov",
        "wmv",
        "flv",
        "m4v",
        "webm",
        "m4v",
    ]

    def __init__(self):
        self.mode = "magic"
        pass

    # def sub(self, pattern, replace, file_name):
    #     """魔法正则、变量替换"""
    #     if not replace:
    #         return file_name
    #     # 预处理替换变量
    #     for key, p_list in self.magic_variable.items():
    #         if key in replace:
    #             # 正则类替换变量
    #             if p_list and isinstance(p_list, list):
    #                 for p in p_list:
    #                     match = re.search(p, file_name)
    #                     if match:
    #                         # 匹配成功，替换为匹配到的值
    #                         value = match.group()
    #                         # 日期格式处理：补全、格式化
    #                         if key == "{DATE}":
    #                             value = "".join(
    #                                 [char for char in value if char.isdigit()]
    #                             )
    #                             value = (
    #                                 str(datetime.now().year)[: (8 - len(value))] + value
    #                             )
    #                         replace = replace.replace(key, value)
    #                         break
    #             # 非正则类替换变量
    #             if key == "{TASKNAME}":
    #                 replace = replace.replace(key, self.magic_variable["{TASKNAME}"])
    #             elif key == "{SXX}" and not match:
    #                 replace = replace.replace(key, "S01")
    #             elif key == "{I}":
    #                 continue
    #             else:
    #                 # 清理未匹配的 magic_variable key
    #                 replace = replace.replace(key, "")
    #     if pattern and replace:
    #         file_name = re.sub(pattern, replace, file_name)
    #     else:
    #         file_name = replace
    #     return file_name

    def set_regex(self, movie: str, tv: str):
        self.movie_regex = movie
        self.tv_regex = tv
        self.title = ""
        self.split_str = ""

    def set_title(self, title: str = "", path: str = ""):
        if title:
            self.title = title
        elif path:
            math_title_for_path_pattern = [
                r"/.*/(.*?) ?\(\d{4}\)",
                r"/.*/(.*)(?=/S\d)",
                r"/.*】([\u4e00-\u9fa5]{2,})(?!\/)",
                r"/([\u4e00-\u9fa5]{2,})(?!\/)",
            ]
            for p in math_title_for_path_pattern:
                match = re.search(p, path)
                if match:
                    self.title = match.group(1)
                    break

    def magic_rename(self, name: str, replace: str) -> str:
        if split_str := re.search(r"\}([\.\-_ ]*)\{", replace):
            self.split_str = split_str.group(1)
        for key, p_list in self.magic_variable.items():
            if key in replace:
                # 正则类替换变量
                if p_list and isinstance(p_list, list):
                    for p in p_list:
                        match = re.search(p, name)
                        if match:
                            value = match.group()
                            replace = replace.replace(key, value)
                # 非正则类替换变量
                if key == "{TITLE}" and not match:
                    replace = replace.replace(key, self.title)
                elif key == "{SXX}" and not match:
                    replace = replace.replace(key, "S01")
                elif key == "{I}":
                    continue
                else:
                    # 清理未匹配的 magic_variable key
                    replace = replace.replace(key, "")
        # 清理多余分隔符
        if self.split_str:
            replace = replace.replace(
                f"{self.split_str}{self.split_str}", self.split_str
            ).strip(self.split_str)
        return replace

    def magic_rename_files(
        self, files: List[Dict[str, Any]], path: str
    ) -> List[Dict[str, Any]]:
        """
        处理文件列表，为每个文件添加 name_magic 字段
        """
        if path:
            self.set_title(path=path)
        for file in files:
            if not file.get("isdir") and file["name"].split(".")[-1] in self.rename_ext:
                file["name_magic"] = self.magic_rename(file["name"], self.tv_regex)
        return files
