import os
import hashlib
import urllib.parse

DEBUG = os.getenv("DEBUG", "").lower() == "true"


def get_app_ver():
    BUILD_SHA = os.getenv("BUILD_SHA", "")
    BUILD_TAG = os.getenv("BUILD_TAG", "")
    if BUILD_TAG[:1] == "v":
        return BUILD_TAG
    elif BUILD_SHA:
        return f"{BUILD_TAG}({BUILD_SHA[:7]})"
    else:
        return "dev"


def is_lan_ip(ip):
    """判断是否为内网 IP"""
    lan_ip_start = ["127.0.0.1", "localhost", "10.", "172.17.", "192.168.", "cnas"]
    for start in lan_ip_start:
        if ip.startswith(start):
            return True
    return False


def url_full_path(url):
    """获取 URL 的完整路径"""
    parsed_url = urllib.parse.urlparse(url)
    query = f"?{parsed_url.query}" if parsed_url.query else ""
    fragment = f"#{parsed_url.fragment}" if parsed_url.fragment else ""
    return f"{parsed_url.path}{query}{fragment}"


def md5(text, length=32):
    """计算给定文本的 MD5 哈希值，并根据指定长度返回"""
    if not isinstance(text, str):
        raise TypeError("Input 'text' must be a string.")
    encoded_text = text.encode("utf-8")
    md5_hash = hashlib.md5()
    md5_hash.update(encoded_text)
    full_md5_hex = md5_hash.hexdigest()
    if length == 32:
        return full_md5_hex
    elif length == 16:
        return full_md5_hex[8:24]
    else:
        raise ValueError("Invalid length. Must be 16 or 32.")
