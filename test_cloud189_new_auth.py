#!/usr/bin/env python3
"""
测试天翼云盘新认证方式
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from drivers.cloud189 import Cloud189Driver

def test_cloud189_new_auth():
    """测试天翼云盘新认证方式"""
    
    # 测试配置 - 需要真实的access_token
    test_config = {
        "access_token": "YOUR_ACCESS_TOKEN_HERE",  # 需要替换为真实的access_token
        "session_key": "",
        "session_secret": "",
        "family_session_key": "",
        "login_name": "",
        "user_info": {}
    }
    
    print("=== 天翼云盘新认证方式测试 ===")
    
    try:
        # 创建驱动实例
        driver = Cloud189Driver(test_config)
        driver.name = "test_cloud189"
        
        print("1. 创建驱动实例成功")
        
        # 测试登录
        print("2. 测试登录认证...")
        if driver._ensure_login():
            print("   ✓ 登录成功")
            print(f"   - 登录名: {driver.login_name}")
            print(f"   - Session Key: {driver.session_key[:20]}...")
            print(f"   - Session Secret: {driver.session_secret[:20]}...")
        else:
            print("   ✗ 登录失败")
            return False
        
        # 测试获取用户信息
        print("3. 测试获取用户信息...")
        if driver.init_info():
            print("   ✓ 获取用户信息成功")
            user_info = driver.user_info
            print(f"   - 用户名: {user_info.get('user_name', 'N/A')}")
            print(f"   - 总空间: {user_info.get('space_info', {}).get('total', 0)} bytes")
            print(f"   - 已用空间: {user_info.get('space_info', {}).get('use', 0)} bytes")
        else:
            print("   ✗ 获取用户信息失败")
        
        # 测试列出根目录文件
        print("4. 测试列出根目录文件...")
        result = driver.list_files("/")
        if result.get("success"):
            files = result.get("data", [])
            print(f"   ✓ 获取文件列表成功，共 {len(files)} 个文件/文件夹")
            for i, file_info in enumerate(files[:5]):  # 只显示前5个
                file_type = "文件夹" if file_info.get("isdir") else "文件"
                print(f"   - {file_type}: {file_info.get('name')}")
            if len(files) > 5:
                print(f"   - ... 还有 {len(files) - 5} 个文件/文件夹")
        else:
            print(f"   ✗ 获取文件列表失败: {result.get('message', 'Unknown error')}")
        
        print("\n=== 测试完成 ===")
        return True
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("天翼云盘新认证方式测试脚本")
    print("注意：需要先在代码中填入真实的access_token")
    print("获取方法：打开天翼云盘APP，抓取请求 api.cloud.189.cn/guns/getPageBanners.action 中的 accessToken")
    print()
    
    # 检查是否设置了access_token
    test_config = {
        "access_token": "YOUR_ACCESS_TOKEN_HERE",
    }
    
    if test_config["access_token"] == "YOUR_ACCESS_TOKEN_HERE":
        print("❌ 请先在代码中设置真实的 access_token")
        print("修改 test_config['access_token'] 的值")
        return
    
    # 运行测试
    success = test_cloud189_new_auth()
    
    if success:
        print("✅ 所有测试通过！新认证方式工作正常。")
    else:
        print("❌ 测试失败，请检查配置和网络连接。")

if __name__ == "__main__":
    main()
